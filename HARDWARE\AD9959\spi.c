/**
 ****************************************************************************************************
 * @file        spi.c
 * <AUTHOR>
 * @version     V1.0
 * @date        2020-04-21
 * @brief       SPI驱动程序(基于STM32标准外设库)
 ****************************************************************************************************
 */

#include "spi.h"
#include "stm32f4xx.h"
#include "../SYSTEM/sys/sys.h"
#include "../SYSTEM/delay/delay.h"

SPI_TypeDef* SPI1_Handler = SPI1; /* SPI1寄存器指针 */

/**
 * @brief       SPI1初始化
 * @param       无
 * @retval      无
 */
void spi1_init(void)
{
    /* 使能SPI1时钟 */
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_SPI1, ENABLE);
    
    /* SPI1配置 */
    SPI_InitTypeDef SPI_InitStructure;
    SPI_InitStructure.SPI_Direction = SPI_Direction_2Lines_FullDuplex;
    SPI_InitStructure.SPI_Mode = SPI_Mode_Master;
    SPI_InitStructure.SPI_DataSize = SPI_DataSize_16b;
    SPI_InitStructure.SPI_CPOL = SPI_CPOL_High;
    SPI_InitStructure.SPI_CPHA = SPI_CPHA_1Edge;
    SPI_InitStructure.SPI_NSS = SPI_NSS_Soft;
    SPI_InitStructure.SPI_BaudRatePrescaler = SPI_BaudRatePrescaler_32;
    SPI_InitStructure.SPI_FirstBit = SPI_FirstBit_MSB;
    SPI_InitStructure.SPI_CRCPolynomial = 7;
    SPI_Init(SPI1, &SPI_InitStructure);
    
    /* 使能SPI1 */
    SPI_Cmd(SPI1, ENABLE);
    spi1_read_write_16bit(0xFFFF); /* 启动传输 */
}

/**
 * @brief       SPI底层驱动初始化
 * @param       hspi: SPI句柄
 * @retval      无
 */
void spi1_gpio_init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    /* 使能GPIOA时钟 */
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
    
    /* 配置SPI1引脚: SCK/MISO/MOSI */
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_5 | GPIO_Pin_6 | GPIO_Pin_7;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    /* 复用功能映射 */
    GPIO_PinAFConfig(GPIOA, GPIO_PinSource5, GPIO_AF_SPI1);
    GPIO_PinAFConfig(GPIOA, GPIO_PinSource6, GPIO_AF_SPI1);
    GPIO_PinAFConfig(GPIOA, GPIO_PinSource7, GPIO_AF_SPI1);
}

/**
 * @brief       SPI1速度设置
 * @param       speed: 分频系数
 * @retval      无
 */
void spi1_set_speed(uint8_t speed)
{
    /* 禁用SPI */
    SPI_Cmd(SPI1, DISABLE);
    
    /* 修改分频系数 */
    SPI1->CR1 &= 0xFFC7;
    SPI1->CR1 |= speed << 3;
    
    /* 重新使能SPI */
    SPI_Cmd(SPI1, ENABLE);
}

/**
 * @brief       SPI1读写16位数据
 * @param       txdata: 要发送的数据
 * @retval      接收到的数据
 */
uint16_t spi1_read_write_16bit(uint16_t txdata)
{
    /* 等待发送缓冲区为空 */
    while(SPI_I2S_GetFlagStatus(SPI1, SPI_I2S_FLAG_TXE) == RESET);
    
    /* 发送数据 */
    SPI_I2S_SendData(SPI1, txdata);
    
    /* 等待接收缓冲区非空 */
    while(SPI_I2S_GetFlagStatus(SPI1, SPI_I2S_FLAG_RXNE) == RESET);
    
    /* 读取接收数据 */
    return SPI_I2S_ReceiveData(SPI1);
}
