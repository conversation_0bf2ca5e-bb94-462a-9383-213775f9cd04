Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to arm_cfft_radix4_init_f32.o(.text) for arm_cfft_radix4_init_f32
    main.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    main.o(.text) refers to usart.o(.text) for uart_init
    main.o(.text) refers to delay.o(.text) for delay_init
    main.o(.text) refers to led.o(.text) for LED_Init
    main.o(.text) refers to adc.o(.text) for Adc_Init
    main.o(.text) refers to ad9959_new.o(.text) for Init_AD9959
    main.o(.text) refers to lcd.o(.text) for lcd_init
    main.o(.text) refers to timer.o(.text) for TIM3_Int_Init
    main.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_Cmd
    main.o(.text) refers to fft.o(.bss) for scfft
    main.o(.text) refers to fft.o(.data) for sampfre
    main.o(.text) refers to lcd.o(.data) for g_point_color
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to timer.o(.text) for TIM4_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(.text) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to adc.o(.text) for DMA2_Stream0_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    stm32f4xx_adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_can.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_cryp.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_dac.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_dma2d.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_fsmc.o(.text) refers to stm32f4xx_fsmc.o(.constdata) for FSMC_DefaultTimingStruct
    stm32f4xx_gpio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_hash.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_i2c.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_ltdc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_pwr.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rng.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_sai.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_sdio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_syscfg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_wwdg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to delay.o(.data) for fac_us
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    usart.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    usart.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    usart.o(.text) refers to usart.o(.data) for Res
    usart.o(.text) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    stm32f4_key.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    stm32f4_key.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    led.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    led.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    g.o(.text) refers to stm32f4xx_usart.o(.text) for USART_SendData
    g.o(.text) refers to delay.o(.text) for delay_ms
    g.o(.text) refers to g.o(.data) for beep
    timer.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    timer.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    timer.o(.text) refers to misc.o(.text) for NVIC_Init
    timer.o(.text) refers to usart.o(.data) for Res
    timer.o(.text) refers to timer.o(.data) for KAISHI
    kalman.o(.text) refers to kalman.o(.data) for current
    kalman.o(.text) refers to kalman.o(.bss) for state
    adc.o(.text) refers to fft.o(.text) for Hanningwindow
    adc.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    adc.o(.text) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    adc.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    adc.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    adc.o(.text) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    adc.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    adc.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_CommonInit
    adc.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    adc.o(.text) refers to stm32f4xx_dma.o(.text) for DMA_DeInit
    adc.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_Cmd
    adc.o(.text) refers to fft.o(.data) for peak1_idx
    adc.o(.text) refers to fft.o(.bss) for fft_inputbuf
    adc.o(.text) refers to adc.o(.data) for phase_A
    adc.o(.text) refers to adc.o(.bss) for buff_adc
    fft.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    fft.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    fft.o(.text) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    fft.o(.text) refers to arm_cfft_radix4_f32.o(.text) for arm_cfft_radix4_f32
    fft.o(.text) refers to arm_cmplx_mag_f32.o(.text) for arm_cmplx_mag_f32
    fft.o(.text) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    fft.o(.text) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    fft.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    fft.o(.text) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    fft.o(.text) refers to fft.o(.data) for i
    fft.o(.text) refers to fft.o(.bss) for fft_inputbuf
    fft.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    fft.o(.text) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    fft.o(.text) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    fft.o(.text) refers to kalman.o(.text) for kalman_thd
    lcd.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lcd.o(.text) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lcd.o(.text) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    lcd.o(.text) refers to lcd.o(.bss) for lcddev
    lcd.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    lcd.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    lcd.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    lcd.o(.text) refers to sys.o(.text) for sys_gpio_pin_set
    lcd.o(.text) refers to delay.o(.text) for delay_ms
    lcd.o(.text) refers to noretval__2printf.o(.text) for __2printf
    lcd.o(.text) refers to lcd_ex.o(.text) for lcd_ex_st7789_reginit
    lcd.o(.text) refers to lcd.o(.constdata) for asc2_1206
    lcd.o(.text) refers to lcd.o(.data) for g_back_color
    lcd_ex.o(.text) refers to lcd.o(.text) for lcd_wr_regno
    lcd_ex.o(.text) refers to delay.o(.text) for delay_ms
    ad9959_new.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_ResetBits
    ad9959_new.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    ad9959_new.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9959_new.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ad9959_new.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    ad9959_new.o(.text) refers to ad9959_new.o(.bss) for AD9959msg
    ad9959_new.o(.text) refers to ad9959_new.o(.data) for CSR_DATA0
    ad9959_new.o(.text) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    ad9959_new.o(.text) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    ad9959_new.o(.text) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    ad9959_new.o(.text) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    ad9959_new.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    spi.o(.text) refers to stm32f4xx_spi.o(.text) for SPI_I2S_GetFlagStatus
    spi.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphClockCmd
    spi.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    arm_cfft_radix4_f32.o(.text) refers to arm_bitreversal.o(.text) for arm_bitreversal_f32
    arm_cfft_radix4_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_4096
    arm_cfft_radix4_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for armBitRevTable
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    atan2.o(i.__hardfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2.o(i.__hardfp_atan2) refers to _rserrno.o(.text) for __set_errno
    atan2.o(i.__hardfp_atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2_x.o(i.____hardfp_atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    cos.o(i.__hardfp_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.__hardfp_cos) refers to _rserrno.o(.text) for __set_errno
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos.o(i.__hardfp_cos) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos.o(i.__hardfp_cos) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos.o(i.__hardfp_cos) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos.o(i.__hardfp_cos) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    cos.o(i.__softfp_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.__softfp_cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos.o(i.cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos_x.o(i.____hardfp_cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.____hardfp_cos$lsc) refers to _rserrno.o(.text) for __set_errno
    cos_x.o(i.____hardfp_cos$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos_x.o(i.____hardfp_cos$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos_x.o(i.____hardfp_cos$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos_x.o(i.____hardfp_cos$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos_x.o(i.____hardfp_cos$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    cos_x.o(i.____softfp_cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.____softfp_cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    cos_x.o(i.__cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.__cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    fmodf.o(i.__hardfp_fmodf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf.o(i.__hardfp_fmodf) refers to frem_clz.o(x$fpl$frem) for _frem
    fmodf.o(i.__hardfp_fmodf) refers to _rserrno.o(.text) for __set_errno
    fmodf.o(i.__hardfp_fmodf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    fmodf.o(i.__softfp_fmodf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf.o(i.__softfp_fmodf) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    fmodf.o(i.fmodf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf.o(i.fmodf) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    fmodf_x.o(i.____hardfp_fmodf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf_x.o(i.____hardfp_fmodf$lsc) refers to frem_clz.o(x$fpl$frem) for _frem
    fmodf_x.o(i.____hardfp_fmodf$lsc) refers to _rserrno.o(.text) for __set_errno
    fmodf_x.o(i.____softfp_fmodf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf_x.o(i.____softfp_fmodf$lsc) refers to fmodf_x.o(i.____hardfp_fmodf$lsc) for ____hardfp_fmodf$lsc
    fmodf_x.o(i.__fmodf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmodf_x.o(i.__fmodf$lsc) refers to fmodf_x.o(i.____hardfp_fmodf$lsc) for ____hardfp_fmodf$lsc
    sin.o(i.__hardfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__hardfp_sin) refers to _rserrno.o(.text) for __set_errno
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin.o(i.__hardfp_sin) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin.o(i.__hardfp_sin) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.__hardfp_sin) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    sin.o(i.__hardfp_sin) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin.o(i.__softfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__softfp_sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin.o(i.sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin_x.o(i.____hardfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____hardfp_sin$lsc) refers to _rserrno.o(.text) for __set_errno
    sin_x.o(i.____hardfp_sin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin_x.o(i.____hardfp_sin$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin_x.o(i.____hardfp_sin$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin_x.o(i.____hardfp_sin$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    sin_x.o(i.____hardfp_sin$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin_x.o(i.____softfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____softfp_sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sin_x.o(i.__sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.__sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(.text) for fputc
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frem_clz.o(x$fpl$frem) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frem_clz.o(x$fpl$frem) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__hardfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.__hardfp_atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.__hardfp_atan) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.____hardfp_atan$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    rred.o(i.__ieee754_rem_pio2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_char_common.o(.text) refers to __printf.o(.text) for __printf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.bss), (50 bytes).
    Removing main.o(.data), (112 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.text), (2544 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(.text), (72 bytes).
    Removing stm32f4xx_cryp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.text), (856 bytes).
    Removing stm32f4xx_cryp_aes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.text), (4586 bytes).
    Removing stm32f4xx_cryp_des.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.text), (472 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.text), (536 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.text), (528 bytes).
    Removing stm32f4xx_dbgmcu.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.text), (100 bytes).
    Removing stm32f4xx_dcmi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.text), (396 bytes).
    Removing stm32f4xx_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.text), (948 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.text), (272 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.text), (1684 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.text), (64 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.text), (1480 bytes).
    Removing stm32f4xx_fsmc.o(.constdata), (28 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.text), (552 bytes).
    Removing stm32f4xx_hash_md5.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.text), (534 bytes).
    Removing stm32f4xx_hash_sha1.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.text), (548 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.text), (1110 bytes).
    Removing stm32f4xx_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.text), (64 bytes).
    Removing stm32f4xx_ltdc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.text), (1672 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.text), (364 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rng.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.text), (160 bytes).
    Removing stm32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.text), (3432 bytes).
    Removing stm32f4xx_sai.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sai.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sai.o(.text), (524 bytes).
    Removing stm32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.text), (476 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(.text), (1152 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.text), (148 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.text), (144 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing stm32f4_key.o(.rev16_text), (4 bytes).
    Removing stm32f4_key.o(.revsh_text), (4 bytes).
    Removing stm32f4_key.o(.text), (524 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing g.o(.rev16_text), (4 bytes).
    Removing g.o(.revsh_text), (4 bytes).
    Removing g.o(.text), (148 bytes).
    Removing g.o(.data), (1 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing kalman.o(.rev16_text), (4 bytes).
    Removing kalman.o(.revsh_text), (4 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing fft.o(.rev16_text), (4 bytes).
    Removing fft.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing lcd_ex.o(.rev16_text), (4 bytes).
    Removing lcd_ex.o(.revsh_text), (4 bytes).
    Removing ad9959_new.o(.rev16_text), (4 bytes).
    Removing ad9959_new.o(.revsh_text), (4 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.text), (280 bytes).
    Removing spi.o(.data), (4 bytes).
    Removing arm_cmplx_mag_f32.o(.rev16_text), (4 bytes).
    Removing arm_cmplx_mag_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_radix4_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix4_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_radix4_init_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix4_init_f32.o(.revsh_text), (4 bytes).
    Removing arm_bitreversal.o(.rev16_text), (4 bytes).
    Removing arm_bitreversal.o(.revsh_text), (4 bytes).
    Removing arm_common_tables.o(.rev16_text), (4 bytes).
    Removing arm_common_tables.o(.revsh_text), (4 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (8192 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).
    Removing arm_common_tables.o(.constdata), (24576 bytes).
    Removing arm_common_tables.o(.constdata), (12288 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (40 bytes).
    Removing arm_common_tables.o(.constdata), (96 bytes).
    Removing arm_common_tables.o(.constdata), (112 bytes).
    Removing arm_common_tables.o(.constdata), (416 bytes).
    Removing arm_common_tables.o(.constdata), (880 bytes).
    Removing arm_common_tables.o(.constdata), (896 bytes).
    Removing arm_common_tables.o(.constdata), (3600 bytes).
    Removing arm_common_tables.o(.constdata), (7616 bytes).
    Removing arm_common_tables.o(.constdata), (8064 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (8192 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).

180 unused section(s) (total 151285 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/frem.s                          0x00000000   Number         0  frem_clz.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos_x.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fmodf.c                       0x00000000   Number         0  fmodf.o ABSOLUTE
    ../mathlib/fmodf.c                       0x00000000   Number         0  fmodf_x.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin_x.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i_x.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ..\CORE\startup_stm32f40_41xxx.s         0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    ..\CommonTables\arm_common_tables.c      0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\ComplexMathFunctions\arm_cmplx_mag_f32.c 0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    ..\FWLIB\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_adc.c             0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_can.c             0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_crc.c             0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp.c            0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_aes.c        0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_des.c        0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_tdes.c       0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dac.c             0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dbgmcu.c          0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dcmi.c            0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma.c             0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma2d.c           0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_exti.c            0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash.c           0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash_ramfunc.c   0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_fsmc.c            0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_gpio.c            0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash.c            0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_md5.c        0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_sha1.c       0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_i2c.c             0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_iwdg.c            0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_ltdc.c            0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_pwr.c             0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rcc.c             0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rng.c             0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rtc.c             0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sai.c             0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sdio.c            0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_spi.c             0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_syscfg.c          0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_tim.c             0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_usart.c           0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_wwdg.c            0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\HARDWARE\AD9959\AD9959_NEW.C          0x00000000   Number         0  ad9959_new.o ABSOLUTE
    ..\HARDWARE\AD9959\spi.c                 0x00000000   Number         0  spi.o ABSOLUTE
    ..\HARDWARE\ADC\adc.c                    0x00000000   Number         0  adc.o ABSOLUTE
    ..\HARDWARE\FFT\fft.c                    0x00000000   Number         0  fft.o ABSOLUTE
    ..\HARDWARE\G\G.c                        0x00000000   Number         0  g.o ABSOLUTE
    ..\HARDWARE\KALMAN\kalman.c              0x00000000   Number         0  kalman.o ABSOLUTE
    ..\HARDWARE\LCD\lcd.c                    0x00000000   Number         0  lcd.o ABSOLUTE
    ..\HARDWARE\LCD\lcd_ex.c                 0x00000000   Number         0  lcd_ex.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\TIMER2\timer.c               0x00000000   Number         0  timer.o ABSOLUTE
    ..\HARDWARE\key\stm32f4_key.c            0x00000000   Number         0  stm32f4_key.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\TransformFunctions\arm_bitreversal.c  0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    ..\TransformFunctions\arm_cfft_radix4_f32.c 0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    ..\TransformFunctions\arm_cfft_radix4_init_f32.c 0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    ..\\CommonTables\\arm_common_tables.c    0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\\ComplexMathFunctions\\arm_cmplx_mag_f32.c 0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    ..\\FWLIB\\src\\misc.c                   0x00000000   Number         0  misc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_adc.c          0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_can.c          0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_crc.c          0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp.c         0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_aes.c     0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_des.c     0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_tdes.c    0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dac.c          0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dbgmcu.c       0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dcmi.c         0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma.c          0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma2d.c        0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_exti.c         0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash.c        0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_fsmc.c         0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_gpio.c         0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash.c         0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_md5.c     0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_sha1.c    0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_i2c.c          0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_iwdg.c         0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_ltdc.c         0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_pwr.c          0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rcc.c          0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rng.c          0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rtc.c          0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sai.c          0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sdio.c         0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_spi.c          0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_syscfg.c       0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_tim.c          0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_usart.c        0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_wwdg.c         0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\\HARDWARE\\AD9959\\AD9959_NEW.C       0x00000000   Number         0  ad9959_new.o ABSOLUTE
    ..\\HARDWARE\\AD9959\\spi.c              0x00000000   Number         0  spi.o ABSOLUTE
    ..\\HARDWARE\\ADC\\adc.c                 0x00000000   Number         0  adc.o ABSOLUTE
    ..\\HARDWARE\\FFT\\fft.c                 0x00000000   Number         0  fft.o ABSOLUTE
    ..\\HARDWARE\\G\\G.c                     0x00000000   Number         0  g.o ABSOLUTE
    ..\\HARDWARE\\KALMAN\\kalman.c           0x00000000   Number         0  kalman.o ABSOLUTE
    ..\\HARDWARE\\LCD\\lcd.c                 0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\HARDWARE\\LCD\\lcd_ex.c              0x00000000   Number         0  lcd_ex.o ABSOLUTE
    ..\\HARDWARE\\LED\\led.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\HARDWARE\\TIMER2\\timer.c            0x00000000   Number         0  timer.o ABSOLUTE
    ..\\HARDWARE\\key\\stm32f4_key.c         0x00000000   Number         0  stm32f4_key.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    ..\\TransformFunctions\\arm_bitreversal.c 0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    ..\\TransformFunctions\\arm_cfft_radix4_f32.c 0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    ..\\TransformFunctions\\arm_cfft_radix4_init_f32.c 0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$0000000C  0x080001fc   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000017  0x08000202   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000206   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000208   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800020c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800020c   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x0800020e   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000210   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000210   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000210   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000210   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000210   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000210   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000210   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000210   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000212   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000212   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000212   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000218   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000218   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800021c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800021c   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000224   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000226   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000226   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800022a   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000230   Section        0  main.o(.text)
    .text                                    0x0800031c   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x08000338   Section        0  system_stm32f4xx.o(.text)
    SetSysClock                              0x08000339   Thumb Code   220  system_stm32f4xx.o(.text)
    .text                                    0x08000548   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x08000548   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08000588   Section        0  misc.o(.text)
    .text                                    0x08000668   Section        0  stm32f4xx_adc.o(.text)
    .text                                    0x08000acc   Section        0  stm32f4xx_dma.o(.text)
    .text                                    0x08000e74   Section        0  stm32f4xx_gpio.o(.text)
    .text                                    0x08001108   Section        0  stm32f4xx_rcc.o(.text)
    .text                                    0x08001764   Section        0  stm32f4xx_tim.o(.text)
    TI4_Config                               0x08001f03   Thumb Code    80  stm32f4xx_tim.o(.text)
    TI3_Config                               0x08001f65   Thumb Code    72  stm32f4xx_tim.o(.text)
    TI2_Config                               0x08001fc7   Thumb Code    90  stm32f4xx_tim.o(.text)
    TI1_Config                               0x08002033   Thumb Code    58  stm32f4xx_tim.o(.text)
    .text                                    0x08002408   Section        0  stm32f4xx_usart.o(.text)
    .text                                    0x0800285c   Section        0  delay.o(.text)
    .text                                    0x08002960   Section        0  sys.o(.text)
    sys_nvic_priority_group_config           0x0800296d   Thumb Code    36  sys.o(.text)
    .text                                    0x08002ed8   Section        0  usart.o(.text)
    .text                                    0x08003034   Section        0  led.o(.text)
    .text                                    0x08003074   Section        0  timer.o(.text)
    .text                                    0x0800314c   Section        0  kalman.o(.text)
    .text                                    0x080032d0   Section        0  adc.o(.text)
    .text                                    0x08003a38   Section        0  fft.o(.text)
    .text                                    0x08004184   Section        0  lcd.o(.text)
    lcd_opt_delay                            0x080041bb   Thumb Code    12  lcd.o(.text)
    lcd_rd_data                              0x080041c7   Thumb Code    20  lcd.o(.text)
    lcd_pow                                  0x080053e9   Thumb Code    22  lcd.o(.text)
    .text                                    0x080055e0   Section        0  lcd_ex.o(.text)
    .text                                    0x08007e88   Section        0  ad9959_new.o(.text)
    .text                                    0x08008d2c   Section        0  arm_cmplx_mag_f32.o(.text)
    .text                                    0x08008e1c   Section        0  arm_cfft_radix4_f32.o(.text)
    .text                                    0x080094bc   Section        0  arm_cfft_radix4_init_f32.o(.text)
    .text                                    0x0800957c   Section        0  arm_bitreversal.o(.text)
    .text                                    0x08009762   Section        2  use_no_semi_2.o(.text)
    .text                                    0x08009764   Section      238  lludivv7m.o(.text)
    .text                                    0x08009854   Section        0  noretval__2printf.o(.text)
    .text                                    0x0800986c   Section        0  __printf.o(.text)
    .text                                    0x080098d4   Section        0  _printf_hex_int.o(.text)
    .text                                    0x0800992c   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08009990   Section        0  heapauxi.o(.text)
    .text                                    0x08009996   Section        2  use_no_semi.o(.text)
    .text                                    0x08009998   Section        0  _rserrno.o(.text)
    .text                                    0x080099ae   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08009a60   Section        0  _printf_char_file.o(.text)
    .text                                    0x08009a84   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08009a8c   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08009a8d   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08009abc   Section        0  ferror.o(.text)
    .text                                    0x08009ac4   Section        8  libspace.o(.text)
    .text                                    0x08009acc   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08009b16   Section        0  exit.o(.text)
    i.__ARM_fpclassify                       0x08009b28   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_atan                          0x08009b58   Section        0  atan.o(i.__hardfp_atan)
    i.__hardfp_atan2                         0x08009e30   Section        0  atan2.o(i.__hardfp_atan2)
    i.__hardfp_cos                           0x0800a030   Section        0  cos.o(i.__hardfp_cos)
    i.__hardfp_fmodf                         0x0800a0f8   Section        0  fmodf.o(i.__hardfp_fmodf)
    i.__hardfp_sin                           0x0800a1a8   Section        0  sin.o(i.__hardfp_sin)
    i.__hardfp_sqrt                          0x0800a270   Section        0  sqrt.o(i.__hardfp_sqrt)
    i.__ieee754_rem_pio2                     0x0800a2f0   Section        0  rred.o(i.__ieee754_rem_pio2)
    i.__kernel_cos                           0x0800a728   Section        0  cos_i.o(i.__kernel_cos)
    i.__kernel_poly                          0x0800a898   Section        0  poly.o(i.__kernel_poly)
    i.__kernel_sin                           0x0800a990   Section        0  sin_i.o(i.__kernel_sin)
    i.__mathlib_dbl_infnan                   0x0800aac0   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x0800aad4   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x0800aae8   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x0800ab08   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__mathlib_flt_invalid                  0x0800ab28   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.atan                                   0x0800ab38   Section        0  atan.o(i.atan)
    i.fabs                                   0x0800ab48   Section        0  fabs.o(i.fabs)
    x$fpl$basic                              0x0800ab60   Section       24  basic.o(x$fpl$basic)
    $v0                                      0x0800ab60   Number         0  basic.o(x$fpl$basic)
    x$fpl$d2f                                0x0800ab78   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x0800ab78   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x0800abdc   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x0800abdc   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x0800abed   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcmpinf                            0x0800ad2c   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x0800ad2c   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x0800ad44   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x0800ad44   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x0800ad4b   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$deqf                               0x0800aff4   Section      120  deqf.o(x$fpl$deqf)
    $v0                                      0x0800aff4   Number         0  deqf.o(x$fpl$deqf)
    x$fpl$dfix                               0x0800b06c   Section       94  dfix.o(x$fpl$dfix)
    $v0                                      0x0800b06c   Number         0  dfix.o(x$fpl$dfix)
    x$fpl$dfixu                              0x0800b0cc   Section       90  dfixu.o(x$fpl$dfixu)
    $v0                                      0x0800b0cc   Number         0  dfixu.o(x$fpl$dfixu)
    x$fpl$dflt                               0x0800b126   Section       46  dflt_clz.o(x$fpl$dflt)
    $v0                                      0x0800b126   Number         0  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x0800b154   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x0800b154   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dleqf                              0x0800b17c   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x0800b17c   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x0800b1f4   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x0800b1f4   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x0800b348   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x0800b348   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x0800b3e4   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x0800b3e4   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x0800b3f0   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x0800b3f0   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$drsb                               0x0800b45c   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x0800b45c   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x0800b474   Section      408  dsqrt_umaal.o(x$fpl$dsqrt)
    $v0                                      0x0800b474   Number         0  dsqrt_umaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x0800b60c   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x0800b60c   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x0800b61d   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x0800b7e0   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x0800b7e0   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x0800b836   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x0800b836   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x0800b8c2   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800b8c2   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$frem                               0x0800b8cc   Section      244  frem_clz.o(x$fpl$frem)
    $v0                                      0x0800b8cc   Number         0  frem_clz.o(x$fpl$frem)
    x$fpl$fretinf                            0x0800b9c0   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x0800b9c0   Number         0  fretinf.o(x$fpl$fretinf)
    .constdata                               0x0800b9ca   Section    12160  lcd.o(.constdata)
    x$fpl$usenofp                            0x0800b9ca   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x0800e94a   Section     2048  arm_common_tables.o(.constdata)
    .constdata                               0x0800f14c   Section    32768  arm_common_tables.o(.constdata)
    .constdata                               0x0801714c   Section       40  _printf_hex_int.o(.constdata)
    uc_hextab                                0x0801714c   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x08017160   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x08017178   Section      152  atan.o(.constdata)
    atanhi                                   0x08017178   Data          32  atan.o(.constdata)
    atanlo                                   0x08017198   Data          32  atan.o(.constdata)
    aTodd                                    0x080171b8   Data          40  atan.o(.constdata)
    aTeven                                   0x080171e0   Data          48  atan.o(.constdata)
    .constdata                               0x08017210   Section       48  cos_i.o(.constdata)
    C                                        0x08017210   Data          48  cos_i.o(.constdata)
    .constdata                               0x08017240   Section        8  qnan.o(.constdata)
    .constdata                               0x08017248   Section      200  rred.o(.constdata)
    pio2s                                    0x08017248   Data          48  rred.o(.constdata)
    twooverpi                                0x08017278   Data         152  rred.o(.constdata)
    .constdata                               0x08017310   Section       40  sin_i.o(.constdata)
    S                                        0x08017310   Data          40  sin_i.o(.constdata)
    .data                                    0x20000000   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x20000014   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x20000014   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x20000024   Section        4  delay.o(.data)
    fac_us                                   0x20000024   Data           1  delay.o(.data)
    fac_ms                                   0x20000026   Data           2  delay.o(.data)
    .data                                    0x20000028   Section       10  usart.o(.data)
    .data                                    0x20000034   Section        4  timer.o(.data)
    .data                                    0x20000038   Section        4  kalman.o(.data)
    .data                                    0x2000003c   Section       39  adc.o(.data)
    .data                                    0x20000064   Section       60  fft.o(.data)
    i                                        0x2000009e   Data           2  fft.o(.data)
    .data                                    0x200000a0   Section        8  lcd.o(.data)
    .data                                    0x200000a8   Section       24  ad9959_new.o(.data)
    .bss                                     0x200000c0   Section      200  usart.o(.bss)
    .bss                                     0x20000188   Section      308  kalman.o(.bss)
    .bss                                     0x200002bc   Section    24576  adc.o(.bss)
    .bss                                     0x200062bc   Section    65680  fft.o(.bss)
    .bss                                     0x2001634c   Section       14  lcd.o(.bss)
    .bss                                     0x2001635c   Section       32  ad9959_new.o(.bss)
    .bss                                     0x2001637c   Section       96  libspace.o(.bss)
    HEAP                                     0x200163e0   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x200163e0   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x200165e0   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x200165e0   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x200169e0   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_x                                0x080001fd   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_percent_end                      0x08000203   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000207   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000209   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x0800020d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x0800020f   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000211   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000211   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000211   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000211   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000211   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000211   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000211   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000211   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000213   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000213   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000213   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000219   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000219   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800021d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800021d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000225   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000227   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000227   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800022b   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    main                                     0x08000231   Thumb Code   202  main.o(.text)
    NMI_Handler                              0x0800031d   Thumb Code     2  stm32f4xx_it.o(.text)
    HardFault_Handler                        0x0800031f   Thumb Code     4  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x08000323   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x08000327   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x0800032b   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x0800032f   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x08000331   Thumb Code     2  stm32f4xx_it.o(.text)
    PendSV_Handler                           0x08000333   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Handler                          0x08000335   Thumb Code     2  stm32f4xx_it.o(.text)
    SystemInit                               0x08000415   Thumb Code    88  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x0800046d   Thumb Code   174  system_stm32f4xx.o(.text)
    Reset_Handler                            0x08000549   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x08000563   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x08000565   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    NVIC_PriorityGroupConfig                 0x08000589   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x08000593   Thumb Code   106  misc.o(.text)
    NVIC_SetVectorTable                      0x080005fd   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x0800060b   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x0800062d   Thumb Code    40  misc.o(.text)
    ADC_DeInit                               0x08000669   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_Init                                 0x0800067f   Thumb Code    74  stm32f4xx_adc.o(.text)
    ADC_StructInit                           0x080006c9   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_CommonInit                           0x080006dd   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_CommonStructInit                     0x080006ff   Thumb Code    12  stm32f4xx_adc.o(.text)
    ADC_Cmd                                  0x0800070b   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogCmd                    0x08000721   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogThresholdsConfig       0x08000731   Thumb Code     6  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogSingleChannelConfig    0x08000737   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_TempSensorVrefintCmd                 0x08000747   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_VBATCmd                              0x08000769   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_RegularChannelConfig                 0x0800078b   Thumb Code   184  stm32f4xx_adc.o(.text)
    ADC_SoftwareStartConv                    0x08000843   Thumb Code    10  stm32f4xx_adc.o(.text)
    ADC_GetSoftwareStartConvStatus           0x0800084d   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_EOCOnEachRegularChannelCmd           0x08000861   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_ContinuousModeCmd                    0x08000877   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_DiscModeChannelCountConfig           0x0800088d   Thumb Code    24  stm32f4xx_adc.o(.text)
    ADC_DiscModeCmd                          0x080008a5   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_GetConversionValue                   0x080008bb   Thumb Code     8  stm32f4xx_adc.o(.text)
    ADC_GetMultiModeConversionValue          0x080008c3   Thumb Code     8  stm32f4xx_adc.o(.text)
    ADC_DMACmd                               0x080008cb   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_DMARequestAfterLastTransferCmd       0x080008e1   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_MultiModeDMARequestAfterLastTransferCmd 0x080008f7   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_InjectedChannelConfig                0x08000919   Thumb Code   130  stm32f4xx_adc.o(.text)
    ADC_InjectedSequencerLengthConfig        0x0800099b   Thumb Code    24  stm32f4xx_adc.o(.text)
    ADC_SetInjectedOffset                    0x080009b3   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_ExternalTrigInjectedConvConfig       0x080009c7   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_ExternalTrigInjectedConvEdgeConfig   0x080009d7   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_SoftwareStartInjectedConv            0x080009e7   Thumb Code    10  stm32f4xx_adc.o(.text)
    ADC_GetSoftwareStartInjectedConvCmdStatus 0x080009f1   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_AutoInjectedConvCmd                  0x08000a05   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_InjectedDiscModeCmd                  0x08000a1b   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_GetInjectedConversionValue           0x08000a31   Thumb Code    28  stm32f4xx_adc.o(.text)
    ADC_ITConfig                             0x08000a4d   Thumb Code    56  stm32f4xx_adc.o(.text)
    ADC_GetFlagStatus                        0x08000a85   Thumb Code    18  stm32f4xx_adc.o(.text)
    ADC_ClearFlag                            0x08000a97   Thumb Code     6  stm32f4xx_adc.o(.text)
    ADC_GetITStatus                          0x08000a9d   Thumb Code    38  stm32f4xx_adc.o(.text)
    ADC_ClearITPendingBit                    0x08000ac3   Thumb Code    10  stm32f4xx_adc.o(.text)
    DMA_DeInit                               0x08000acd   Thumb Code   324  stm32f4xx_dma.o(.text)
    DMA_Init                                 0x08000c11   Thumb Code    82  stm32f4xx_dma.o(.text)
    DMA_StructInit                           0x08000c63   Thumb Code    34  stm32f4xx_dma.o(.text)
    DMA_Cmd                                  0x08000c85   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_PeriphIncOffsetSizeConfig            0x08000c9b   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_FlowControllerConfig                 0x08000cb1   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_SetCurrDataCounter                   0x08000cc7   Thumb Code     4  stm32f4xx_dma.o(.text)
    DMA_GetCurrDataCounter                   0x08000ccb   Thumb Code     8  stm32f4xx_dma.o(.text)
    DMA_DoubleBufferModeConfig               0x08000cd3   Thumb Code    24  stm32f4xx_dma.o(.text)
    DMA_DoubleBufferModeCmd                  0x08000ceb   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_MemoryTargetConfig                   0x08000d01   Thumb Code    10  stm32f4xx_dma.o(.text)
    DMA_GetCurrentMemoryTarget               0x08000d0b   Thumb Code    20  stm32f4xx_dma.o(.text)
    DMA_GetCmdStatus                         0x08000d1f   Thumb Code    20  stm32f4xx_dma.o(.text)
    DMA_GetFIFOStatus                        0x08000d33   Thumb Code    12  stm32f4xx_dma.o(.text)
    DMA_GetFlagStatus                        0x08000d3f   Thumb Code    56  stm32f4xx_dma.o(.text)
    DMA_ClearFlag                            0x08000d77   Thumb Code    40  stm32f4xx_dma.o(.text)
    DMA_ITConfig                             0x08000d9f   Thumb Code    58  stm32f4xx_dma.o(.text)
    DMA_GetITStatus                          0x08000dd9   Thumb Code    84  stm32f4xx_dma.o(.text)
    DMA_ClearITPendingBit                    0x08000e2d   Thumb Code    40  stm32f4xx_dma.o(.text)
    GPIO_DeInit                              0x08000e75   Thumb Code   268  stm32f4xx_gpio.o(.text)
    GPIO_Init                                0x08000f81   Thumb Code   144  stm32f4xx_gpio.o(.text)
    GPIO_StructInit                          0x08001011   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_PinLockConfig                       0x08001023   Thumb Code    34  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x08001045   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputData                       0x08001057   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x0800105f   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputData                      0x08001071   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_SetBits                             0x08001079   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ResetBits                           0x0800107d   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_WriteBit                            0x08001081   Thumb Code    10  stm32f4xx_gpio.o(.text)
    GPIO_Write                               0x0800108b   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ToggleBits                          0x0800108f   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_PinAFConfig                         0x08001097   Thumb Code    70  stm32f4xx_gpio.o(.text)
    RCC_DeInit                               0x08001109   Thumb Code    82  stm32f4xx_rcc.o(.text)
    RCC_HSEConfig                            0x0800115b   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_GetFlagStatus                        0x08001169   Thumb Code    60  stm32f4xx_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x080011a5   Thumb Code    56  stm32f4xx_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x080011dd   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_HSICmd                               0x080011f1   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_LSEConfig                            0x080011f7   Thumb Code    46  stm32f4xx_rcc.o(.text)
    RCC_LSICmd                               0x08001225   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLConfig                            0x0800122b   Thumb Code    32  stm32f4xx_rcc.o(.text)
    RCC_PLLCmd                               0x0800124b   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SConfig                         0x08001251   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SCmd                            0x0800125f   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLSAIConfig                         0x08001265   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PLLSAICmd                            0x08001279   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x0800127f   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_MCO1Config                           0x08001285   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_MCO2Config                           0x080012a1   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SYSCLKConfig                         0x080012bd   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x080012d1   Thumb Code    12  stm32f4xx_rcc.o(.text)
    RCC_HCLKConfig                           0x080012dd   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK1Config                          0x080012f1   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK2Config                          0x08001305   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_GetClocksFreq                        0x0800131b   Thumb Code   222  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKConfig                         0x080013f9   Thumb Code    54  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKCmd                            0x0800142f   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_BackupResetCmd                       0x08001437   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_I2SCLKConfig                         0x0800143f   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLI2SClkDivConfig                0x08001445   Thumb Code    26  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLSAIClkDivConfig                0x0800145f   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockACLKConfig                   0x0800147b   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockBCLKConfig                   0x0800148f   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_LTDCCLKDivConfig                     0x080014a3   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_TIMCLKPresConfig                     0x080014b7   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockCmd                   0x080014bd   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockCmd                   0x080014df   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockCmd                   0x0800152d   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x0800154f   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x08001571   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphResetCmd                   0x08001593   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphResetCmd                   0x080015b5   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphResetCmd                   0x080015d7   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x080015f9   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x0800161b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockLPModeCmd             0x0800163d   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockLPModeCmd             0x0800165f   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockLPModeCmd             0x08001681   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockLPModeCmd             0x080016a3   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockLPModeCmd             0x080016c5   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_LSEModeConfig                        0x080016e7   Thumb Code    40  stm32f4xx_rcc.o(.text)
    RCC_ITConfig                             0x0800170f   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_ClearFlag                            0x08001731   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_GetITStatus                          0x08001743   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_ClearITPendingBit                    0x08001759   Thumb Code     8  stm32f4xx_rcc.o(.text)
    TIM_DeInit                               0x08001765   Thumb Code   346  stm32f4xx_tim.o(.text)
    TIM_TimeBaseInit                         0x080018bf   Thumb Code   104  stm32f4xx_tim.o(.text)
    TIM_TimeBaseStructInit                   0x08001927   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PrescalerConfig                      0x08001939   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_CounterModeConfig                    0x0800193f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetCounter                           0x08001951   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetAutoreload                        0x08001955   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetCounter                           0x08001959   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetPrescaler                         0x0800195f   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_UpdateDisableConfig                  0x08001965   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_UpdateRequestConfig                  0x0800197d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ARRPreloadConfig                     0x08001995   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_SelectOnePulseMode                   0x080019ad   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetClockDivision                     0x080019bf   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_Cmd                                  0x080019d1   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1Init                              0x080019e9   Thumb Code   114  stm32f4xx_tim.o(.text)
    TIM_OC2Init                              0x08001a5b   Thumb Code   154  stm32f4xx_tim.o(.text)
    TIM_OC3Init                              0x08001af5   Thumb Code   204  stm32f4xx_tim.o(.text)
    TIM_OC4Init                              0x08001bc1   Thumb Code   112  stm32f4xx_tim.o(.text)
    TIM_OCStructInit                         0x08001c31   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_SelectOCxM                           0x08001c45   Thumb Code    86  stm32f4xx_tim.o(.text)
    TIM_SetCompare1                          0x08001c9b   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare2                          0x08001c9f   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare3                          0x08001ca3   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare4                          0x08001ca7   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_ForcedOC1Config                      0x08001cab   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC2Config                      0x08001cbd   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ForcedOC3Config                      0x08001cd7   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC4Config                      0x08001ce9   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1PreloadConfig                     0x08001d03   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PreloadConfig                     0x08001d15   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PreloadConfig                     0x08001d2f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4PreloadConfig                     0x08001d41   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1FastConfig                        0x08001d5b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2FastConfig                        0x08001d6d   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3FastConfig                        0x08001d87   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4FastConfig                        0x08001d99   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ClearOC1Ref                          0x08001db3   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC2Ref                          0x08001dc5   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ClearOC3Ref                          0x08001ddd   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC4Ref                          0x08001def   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1PolarityConfig                    0x08001e07   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC1NPolarityConfig                   0x08001e19   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PolarityConfig                    0x08001e2b   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC2NPolarityConfig                   0x08001e45   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PolarityConfig                    0x08001e5f   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3NPolarityConfig                   0x08001e79   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC4PolarityConfig                    0x08001e93   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_CCxCmd                               0x08001ead   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_CCxNCmd                              0x08001ecb   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SetIC4Prescaler                      0x08001ee9   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC3Prescaler                      0x08001f53   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetIC2Prescaler                      0x08001fad   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC1Prescaler                      0x08002021   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ICInit                               0x0800206d   Thumb Code   110  stm32f4xx_tim.o(.text)
    TIM_ICStructInit                         0x080020db   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PWMIConfig                           0x080020ed   Thumb Code   124  stm32f4xx_tim.o(.text)
    TIM_GetCapture1                          0x08002169   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture2                          0x0800216f   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture3                          0x08002175   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture4                          0x0800217b   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_BDTRConfig                           0x08002181   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_BDTRStructInit                       0x080021a1   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x080021b3   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SelectCOM                            0x080021d1   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_CCPreloadControl                     0x080021e9   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ITConfig                             0x08002201   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_GenerateEvent                        0x08002213   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetFlagStatus                        0x08002217   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearFlag                            0x08002229   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetITStatus                          0x0800222f   Thumb Code    34  stm32f4xx_tim.o(.text)
    TIM_ClearITPendingBit                    0x08002251   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_DMAConfig                            0x08002257   Thumb Code    10  stm32f4xx_tim.o(.text)
    TIM_DMACmd                               0x08002261   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectCCDMA                          0x08002273   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_InternalClockConfig                  0x0800228b   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_SelectInputTrigger                   0x08002297   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ITRxExternalClockConfig              0x080022a9   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_TIxExternalClockConfig               0x080022c1   Thumb Code    62  stm32f4xx_tim.o(.text)
    TIM_ETRConfig                            0x080022ff   Thumb Code    28  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode1Config                  0x0800231b   Thumb Code    54  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode2Config                  0x08002351   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_SelectOutputTrigger                  0x08002371   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectSlaveMode                      0x08002383   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectMasterSlaveMode                0x08002395   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_EncoderInterfaceConfig               0x080023a7   Thumb Code    66  stm32f4xx_tim.o(.text)
    TIM_SelectHallSensor                     0x080023e9   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_RemapConfig                          0x08002401   Thumb Code     6  stm32f4xx_tim.o(.text)
    USART_DeInit                             0x08002409   Thumb Code   206  stm32f4xx_usart.o(.text)
    USART_Init                               0x080024d7   Thumb Code   204  stm32f4xx_usart.o(.text)
    USART_StructInit                         0x080025a3   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_ClockInit                          0x080025bb   Thumb Code    32  stm32f4xx_usart.o(.text)
    USART_ClockStructInit                    0x080025db   Thumb Code    12  stm32f4xx_usart.o(.text)
    USART_Cmd                                0x080025e7   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetPrescaler                       0x080025ff   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_OverSampling8Cmd                   0x0800260f   Thumb Code    22  stm32f4xx_usart.o(.text)
    USART_OneBitMethodCmd                    0x08002625   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendData                           0x0800263d   Thumb Code     8  stm32f4xx_usart.o(.text)
    USART_ReceiveData                        0x08002645   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_SetAddress                         0x0800264f   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x08002661   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_WakeUpConfig                       0x08002679   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x0800268b   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINCmd                             0x0800269d   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendBreak                          0x080026b5   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_HalfDuplexCmd                      0x080026bf   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetGuardTime                       0x080026d7   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_SmartCardCmd                       0x080026e7   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SmartCardNACKCmd                   0x080026ff   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_IrDAConfig                         0x08002717   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_IrDACmd                            0x08002729   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_DMACmd                             0x08002741   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ITConfig                           0x08002753   Thumb Code    74  stm32f4xx_usart.o(.text)
    USART_GetFlagStatus                      0x0800279d   Thumb Code    26  stm32f4xx_usart.o(.text)
    USART_ClearFlag                          0x080027b7   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_GetITStatus                        0x080027c9   Thumb Code   118  stm32f4xx_usart.o(.text)
    USART_ClearITPendingBit                  0x0800283f   Thumb Code    30  stm32f4xx_usart.o(.text)
    delay_init                               0x0800285d   Thumb Code    52  delay.o(.text)
    delay_us                                 0x08002891   Thumb Code    72  delay.o(.text)
    delay_xms                                0x080028d9   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x08002921   Thumb Code    56  delay.o(.text)
    sys_nvic_set_vector_table                0x08002961   Thumb Code    12  sys.o(.text)
    sys_nvic_init                            0x08002991   Thumb Code   116  sys.o(.text)
    sys_nvic_ex_config                       0x08002a05   Thumb Code   272  sys.o(.text)
    sys_gpio_af_set                          0x08002b15   Thumb Code   104  sys.o(.text)
    sys_gpio_set                             0x08002b7d   Thumb Code   222  sys.o(.text)
    sys_gpio_pin_set                         0x08002c5b   Thumb Code    14  sys.o(.text)
    sys_gpio_pin_get                         0x08002c69   Thumb Code    16  sys.o(.text)
    sys_wfi_set                              0x08002c79   Thumb Code     4  sys.o(.text)
    sys_intx_disable                         0x08002c7d   Thumb Code     4  sys.o(.text)
    sys_intx_enable                          0x08002c81   Thumb Code     4  sys.o(.text)
    sys_msr_msp                              0x08002c85   Thumb Code    10  sys.o(.text)
    sys_standby                              0x08002c8f   Thumb Code    72  sys.o(.text)
    sys_soft_reset                           0x08002cd7   Thumb Code    12  sys.o(.text)
    sys_clock_set                            0x08002ce3   Thumb Code   434  sys.o(.text)
    sys_stm32_clock_init                     0x08002e95   Thumb Code    60  sys.o(.text)
    _sys_exit                                0x08002ed9   Thumb Code     4  usart.o(.text)
    fputc                                    0x08002edd   Thumb Code    22  usart.o(.text)
    uart_init                                0x08002ef3   Thumb Code   164  usart.o(.text)
    USART1_IRQHandler                        0x08002f97   Thumb Code   138  usart.o(.text)
    LED_Init                                 0x08003035   Thumb Code    60  led.o(.text)
    TIM3_Int_Init                            0x08003075   Thumb Code    48  timer.o(.text)
    TIM4_Int_Init                            0x080030a5   Thumb Code    88  timer.o(.text)
    TIM4_IRQHandler                          0x080030fd   Thumb Code    60  timer.o(.text)
    Kalman_init                              0x0800314d   Thumb Code    42  kalman.o(.text)
    kalman_filter                            0x08003177   Thumb Code   154  kalman.o(.text)
    kalman                                   0x08003211   Thumb Code    92  kalman.o(.text)
    kalman_thd                               0x0800326d   Thumb Code    62  kalman.o(.text)
    QCZ_FFT                                  0x080032d1   Thumb Code   330  adc.o(.text)
    QCZ_FFT1                                 0x0800341b   Thumb Code   202  adc.o(.text)
    Adc_Init                                 0x080034e5   Thumb Code   188  adc.o(.text)
    DMA1_Init                                0x080035a1   Thumb Code   180  adc.o(.text)
    DMA2_Stream0_IRQHandler                  0x08003655   Thumb Code    36  adc.o(.text)
    Adc2_Init                                0x08003679   Thumb Code   268  adc.o(.text)
    DMA2_Init                                0x08003785   Thumb Code   184  adc.o(.text)
    DMA2_Stream2_IRQHandler                  0x0800383d   Thumb Code    36  adc.o(.text)
    Adc3_Init                                0x08003861   Thumb Code   188  adc.o(.text)
    DMA3_Init                                0x0800391d   Thumb Code   198  adc.o(.text)
    DMA2_Stream1_IRQHandler                  0x080039e3   Thumb Code    40  adc.o(.text)
    find_peak_indices                        0x08003a39   Thumb Code   174  fft.o(.text)
    FFT                                      0x08003ae7   Thumb Code   158  fft.o(.text)
    Hanningwindow                            0x08003b85   Thumb Code   120  fft.o(.text)
    get_pianyik                              0x08003bfd   Thumb Code   150  fft.o(.text)
    Get_basevpp_point                        0x08003c93   Thumb Code    74  fft.o(.text)
    get_basefrevpp                           0x08003cdd   Thumb Code   274  fft.o(.text)
    Get_othervpp_point                       0x08003def   Thumb Code    38  fft.o(.text)
    Get_vpp_fre                              0x08003e15   Thumb Code   412  fft.o(.text)
    n_get_vppfre                             0x08003fb1   Thumb Code   172  fft.o(.text)
    get_thd                                  0x0800405d   Thumb Code   248  fft.o(.text)
    lcd_wr_data                              0x08004185   Thumb Code    18  lcd.o(.text)
    lcd_wr_regno                             0x08004197   Thumb Code    20  lcd.o(.text)
    lcd_write_reg                            0x080041ab   Thumb Code    16  lcd.o(.text)
    lcd_write_ram_prepare                    0x080041db   Thumb Code    12  lcd.o(.text)
    lcd_set_cursor                           0x080041e7   Thumb Code   282  lcd.o(.text)
    lcd_read_point                           0x08004301   Thumb Code   148  lcd.o(.text)
    lcd_display_on                           0x08004395   Thumb Code    32  lcd.o(.text)
    lcd_display_off                          0x080043b5   Thumb Code    32  lcd.o(.text)
    lcd_scan_dir                             0x080043d5   Thumb Code   578  lcd.o(.text)
    lcd_draw_point                           0x08004617   Thumb Code    26  lcd.o(.text)
    lcd_ssd_backlight_set                    0x08004631   Thumb Code    88  lcd.o(.text)
    lcd_display_dir                          0x08004689   Thumb Code   370  lcd.o(.text)
    lcd_set_window                           0x080047fb   Thumb Code   370  lcd.o(.text)
    lcd_clear                                0x0800496d   Thumb Code    64  lcd.o(.text)
    lcd_init                                 0x080049ad   Thumb Code  1480  lcd.o(.text)
    lcd_fill                                 0x08004f75   Thumb Code    80  lcd.o(.text)
    lcd_color_fill                           0x08004fc5   Thumb Code    92  lcd.o(.text)
    lcd_draw_line                            0x08005021   Thumb Code   172  lcd.o(.text)
    lcd_draw_hline                           0x080050cd   Thumb Code    52  lcd.o(.text)
    lcd_draw_rectangle                       0x08005101   Thumb Code    74  lcd.o(.text)
    lcd_draw_circle                          0x0800514b   Thumb Code   200  lcd.o(.text)
    lcd_fill_circle                          0x08005213   Thumb Code   176  lcd.o(.text)
    lcd_show_char                            0x080052c3   Thumb Code   294  lcd.o(.text)
    lcd_show_num                             0x080053ff   Thumb Code   152  lcd.o(.text)
    lcd_show_xnum                            0x08005497   Thumb Code   198  lcd.o(.text)
    lcd_show_string                          0x0800555d   Thumb Code   106  lcd.o(.text)
    lcd_ex_st7789_reginit                    0x080055e1   Thumb Code   424  lcd_ex.o(.text)
    lcd_ex_ili9341_reginit                   0x08005789   Thumb Code   556  lcd_ex.o(.text)
    lcd_ex_nt35310_reginit                   0x080059b5   Thumb Code  3826  lcd_ex.o(.text)
    lcd_ex_st7796_reginit                    0x080068a7   Thumb Code   454  lcd_ex.o(.text)
    lcd_ex_nt35510_reginit                   0x08006a6d   Thumb Code  3950  lcd_ex.o(.text)
    lcd_ex_ili9806_reginit                   0x080079db   Thumb Code   832  lcd_ex.o(.text)
    lcd_ex_ssd1963_reginit                   0x08007d1b   Thumb Code   366  lcd_ex.o(.text)
    delay1                                   0x08007e89   Thumb Code    18  ad9959_new.o(.text)
    IO_Update                                0x08007e9b   Thumb Code    46  ad9959_new.o(.text)
    WriteData_AD9959                         0x08007ec9   Thumb Code   222  ad9959_new.o(.text)
    WriteFreq                                0x08007fa7   Thumb Code   214  ad9959_new.o(.text)
    WriteAmplitude                           0x0800807d   Thumb Code   162  ad9959_new.o(.text)
    IntReset                                 0x0800811f   Thumb Code    46  ad9959_new.o(.text)
    Intserve                                 0x0800814d   Thumb Code    50  ad9959_new.o(.text)
    AD9959MSGInit                            0x0800817f   Thumb Code    46  ad9959_new.o(.text)
    Init_AD9959                              0x080081ad   Thumb Code   322  ad9959_new.o(.text)
    Write_frequence                          0x080082ef   Thumb Code   212  ad9959_new.o(.text)
    WritePhase                               0x080083c3   Thumb Code   158  ad9959_new.o(.text)
    WriteFreqOrtho                           0x08008461   Thumb Code   448  ad9959_new.o(.text)
    Write_Amplitude                          0x08008621   Thumb Code   204  ad9959_new.o(.text)
    Phase_2_AD9959                           0x080086ed   Thumb Code    80  ad9959_new.o(.text)
    Write_Phase                              0x0800873d   Thumb Code   152  ad9959_new.o(.text)
    ReadData_AD9959                          0x080087d5   Thumb Code   242  ad9959_new.o(.text)
    CalculateStayTime                        0x080088c7   Thumb Code   114  ad9959_new.o(.text)
    CalculateFreq                            0x08008939   Thumb Code    62  ad9959_new.o(.text)
    AD9959FreqSweep                          0x08008977   Thumb Code   528  ad9959_new.o(.text)
    Channel_Select                           0x08008b87   Thumb Code    62  ad9959_new.o(.text)
    SweepFrequency                           0x08008bc5   Thumb Code   300  ad9959_new.o(.text)
    CalculatePhase                           0x08008cf1   Thumb Code    40  ad9959_new.o(.text)
    arm_cmplx_mag_f32                        0x08008d2d   Thumb Code   236  arm_cmplx_mag_f32.o(.text)
    arm_radix4_butterfly_f32                 0x08008e1d   Thumb Code   800  arm_cfft_radix4_f32.o(.text)
    arm_radix4_butterfly_inverse_f32         0x0800913d   Thumb Code   836  arm_cfft_radix4_f32.o(.text)
    arm_cfft_radix4_f32                      0x08009481   Thumb Code    60  arm_cfft_radix4_f32.o(.text)
    arm_cfft_radix4_init_f32                 0x080094bd   Thumb Code   146  arm_cfft_radix4_init_f32.o(.text)
    arm_bitreversal_f32                      0x0800957d   Thumb Code   178  arm_bitreversal.o(.text)
    arm_bitreversal_q31                      0x0800962f   Thumb Code   180  arm_bitreversal.o(.text)
    arm_bitreversal_q15                      0x080096e3   Thumb Code   128  arm_bitreversal.o(.text)
    __use_no_semihosting                     0x08009763   Thumb Code     2  use_no_semi_2.o(.text)
    __aeabi_uldivmod                         0x08009765   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08009765   Thumb Code   238  lludivv7m.o(.text)
    __2printf                                0x08009855   Thumb Code    20  noretval__2printf.o(.text)
    __printf                                 0x0800986d   Thumb Code   104  __printf.o(.text)
    _printf_int_hex                          0x080098d5   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x080098d5   Thumb Code     0  _printf_hex_int.o(.text)
    __aeabi_memcpy4                          0x0800992d   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x0800992d   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x0800992d   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08009975   Thumb Code     0  rt_memcpy_w.o(.text)
    __use_two_region_memory                  0x08009991   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08009993   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08009995   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08009997   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08009997   Thumb Code     2  use_no_semi.o(.text)
    __read_errno                             0x08009999   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x080099a3   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x080099af   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_char_file                        0x08009a61   Thumb Code    32  _printf_char_file.o(.text)
    __aeabi_errno_addr                       0x08009a85   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08009a85   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08009a85   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _printf_char_common                      0x08009a97   Thumb Code    32  _printf_char_common.o(.text)
    ferror                                   0x08009abd   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x08009ac5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08009ac5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08009ac5   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08009acd   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08009b17   Thumb Code    18  exit.o(.text)
    __ARM_fpclassify                         0x08009b29   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_atan                            0x08009b59   Thumb Code   622  atan.o(i.__hardfp_atan)
    __hardfp_atan2                           0x08009e31   Thumb Code   448  atan2.o(i.__hardfp_atan2)
    __hardfp_cos                             0x0800a031   Thumb Code   180  cos.o(i.__hardfp_cos)
    __hardfp_fmodf                           0x0800a0f9   Thumb Code   176  fmodf.o(i.__hardfp_fmodf)
    __hardfp_sin                             0x0800a1a9   Thumb Code   180  sin.o(i.__hardfp_sin)
    __hardfp_sqrt                            0x0800a271   Thumb Code   122  sqrt.o(i.__hardfp_sqrt)
    __ieee754_rem_pio2                       0x0800a2f1   Thumb Code   938  rred.o(i.__ieee754_rem_pio2)
    __kernel_cos                             0x0800a729   Thumb Code   322  cos_i.o(i.__kernel_cos)
    __kernel_poly                            0x0800a899   Thumb Code   248  poly.o(i.__kernel_poly)
    __kernel_sin                             0x0800a991   Thumb Code   280  sin_i.o(i.__kernel_sin)
    __mathlib_dbl_infnan                     0x0800aac1   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x0800aad5   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x0800aae9   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x0800ab09   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    __mathlib_flt_invalid                    0x0800ab29   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    atan                                     0x0800ab39   Thumb Code    16  atan.o(i.atan)
    fabs                                     0x0800ab49   Thumb Code    24  fabs.o(i.fabs)
    __aeabi_dneg                             0x0800ab61   Thumb Code     0  basic.o(x$fpl$basic)
    _dneg                                    0x0800ab61   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_fneg                             0x0800ab67   Thumb Code     0  basic.o(x$fpl$basic)
    _fneg                                    0x0800ab67   Thumb Code     6  basic.o(x$fpl$basic)
    _dabs                                    0x0800ab6d   Thumb Code     6  basic.o(x$fpl$basic)
    _fabs                                    0x0800ab73   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_d2f                              0x0800ab79   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x0800ab79   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x0800abdd   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x0800abdd   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcmp_Inf                           0x0800ad2d   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x0800ad45   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x0800ad45   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_cdcmpeq                          0x0800aff5   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x0800aff5   Thumb Code   120  deqf.o(x$fpl$deqf)
    __aeabi_d2iz                             0x0800b06d   Thumb Code     0  dfix.o(x$fpl$dfix)
    _dfix                                    0x0800b06d   Thumb Code    94  dfix.o(x$fpl$dfix)
    __aeabi_d2uiz                            0x0800b0cd   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x0800b0cd   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_i2d                              0x0800b127   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x0800b127   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x0800b155   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x0800b155   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_cdcmple                          0x0800b17d   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x0800b17d   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x0800b1df   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x0800b1f5   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x0800b1f5   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x0800b349   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x0800b3e5   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x0800b3f1   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x0800b3f1   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_drsub                            0x0800b45d   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x0800b45d   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x0800b475   Thumb Code   404  dsqrt_umaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x0800b60d   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x0800b60d   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x0800b7e1   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x0800b7e1   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x0800b837   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x0800b8c3   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x0800b8cb   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x0800b8cb   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _frem                                    0x0800b8cd   Thumb Code   240  frem_clz.o(x$fpl$frem)
    __fpl_fretinf                            0x0800b9c1   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __I$use$fp                               0x0800b9ca   Number         0  usenofp.o(x$fpl$usenofp)
    asc2_1206                                0x0800b9ca   Data        1140  lcd.o(.constdata)
    asc2_1608                                0x0800be3e   Data        1520  lcd.o(.constdata)
    asc2_2412                                0x0800c42e   Data        3420  lcd.o(.constdata)
    asc2_3216                                0x0800d18a   Data        6080  lcd.o(.constdata)
    armBitRevTable                           0x0800e94a   Data        2048  arm_common_tables.o(.constdata)
    twiddleCoef_4096                         0x0800f14c   Data       32768  arm_common_tables.o(.constdata)
    __mathlib_zero                           0x08017240   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x08017338   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08017358   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x20000004   Data          16  system_stm32f4xx.o(.data)
    Res                                      0x20000028   Data           1  usart.o(.data)
    __stdout                                 0x2000002c   Data           4  usart.o(.data)
    USART_RX_STA                             0x20000030   Data           2  usart.o(.data)
    KAISHI                                   0x20000034   Data           4  timer.o(.data)
    last                                     0x20000038   Data           1  kalman.o(.data)
    current                                  0x20000039   Data           1  kalman.o(.data)
    last1                                    0x2000003a   Data           1  kalman.o(.data)
    current1                                 0x2000003b   Data           1  kalman.o(.data)
    Adresult1                                0x2000003c   Data           4  adc.o(.data)
    Adresult2                                0x20000040   Data           4  adc.o(.data)
    Adresult3                                0x20000044   Data           4  adc.o(.data)
    frequency1                               0x20000048   Data           4  adc.o(.data)
    frequency2                               0x2000004c   Data           4  adc.o(.data)
    frequency3                               0x20000050   Data           4  adc.o(.data)
    phase                                    0x20000054   Data           4  adc.o(.data)
    phase_A                                  0x20000058   Data           4  adc.o(.data)
    phase_B                                  0x2000005c   Data           4  adc.o(.data)
    flag_ADC                                 0x20000060   Data           1  adc.o(.data)
    flag_ADC1                                0x20000061   Data           1  adc.o(.data)
    flag_ADC2                                0x20000062   Data           1  adc.o(.data)
    sampfre                                  0x20000064   Data           4  fft.o(.data)
    flag3                                    0x20000068   Data           1  fft.o(.data)
    Adresult                                 0x2000006c   Data           4  fft.o(.data)
    thd                                      0x20000070   Data           4  fft.o(.data)
    k                                        0x20000074   Data           4  fft.o(.data)
    frequency                                0x20000078   Data           4  fft.o(.data)
    u                                        0x2000007c   Data           1  fft.o(.data)
    elec                                     0x20000080   Data           4  fft.o(.data)
    set_right                                0x20000084   Data           4  fft.o(.data)
    set_rightk                               0x20000088   Data           4  fft.o(.data)
    len                                      0x2000008c   Data           1  fft.o(.data)
    peak1_idx                                0x20000090   Data           4  fft.o(.data)
    peak2_idx                                0x20000094   Data           4  fft.o(.data)
    length                                   0x20000098   Data           4  fft.o(.data)
    timef                                    0x2000009c   Data           2  fft.o(.data)
    g_point_color                            0x200000a0   Data           4  lcd.o(.data)
    g_back_color                             0x200000a4   Data           4  lcd.o(.data)
    CSR_DATA0                                0x200000a8   Data           1  ad9959_new.o(.data)
    CSR_DATA1                                0x200000a9   Data           1  ad9959_new.o(.data)
    CSR_DATA2                                0x200000aa   Data           1  ad9959_new.o(.data)
    CSR_DATA3                                0x200000ab   Data           1  ad9959_new.o(.data)
    FR1_DATA                                 0x200000ac   Data           3  ad9959_new.o(.data)
    FR2_DATA                                 0x200000af   Data           2  ad9959_new.o(.data)
    CFR_DATA                                 0x200000b1   Data           3  ad9959_new.o(.data)
    CPOW0_DATA                               0x200000b4   Data           2  ad9959_new.o(.data)
    LSRR_DATA                                0x200000b6   Data           2  ad9959_new.o(.data)
    RDW_DATA                                 0x200000b8   Data           4  ad9959_new.o(.data)
    FDW_DATA                                 0x200000bc   Data           4  ad9959_new.o(.data)
    USART_RX_BUF                             0x200000c0   Data         200  usart.o(.bss)
    state                                    0x20000188   Data         280  kalman.o(.bss)
    state1                                   0x200002a0   Data          28  kalman.o(.bss)
    buff_adc                                 0x200002bc   Data        8192  adc.o(.bss)
    buff_adc2                                0x200022bc   Data        8192  adc.o(.bss)
    buff_adc3                                0x200042bc   Data        8192  adc.o(.bss)
    scfft                                    0x200062bc   Data          20  fft.o(.bss)
    fft_inputbuf                             0x200062d0   Data       32768  fft.o(.bss)
    fft_outputbuf                            0x2000e2d0   Data       16384  fft.o(.bss)
    Vpp_buff                                 0x200122d0   Data          20  fft.o(.bss)
    fre                                      0x200122e4   Data          20  fft.o(.bss)
    ele                                      0x200122f8   Data          20  fft.o(.bss)
    all_vpp_fre                              0x2001230c   Data          80  fft.o(.bss)
    effective_value                          0x2001235c   Data          24  fft.o(.bss)
    n                                        0x20012374   Data        8172  fft.o(.bss)
    m                                        0x20014360   Data        8172  fft.o(.bss)
    lcddev                                   0x2001634c   Data          14  lcd.o(.bss)
    AD9959msg                                0x2001635c   Data          32  ad9959_new.o(.bss)
    __libspace_start                         0x2001637c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200163dc   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00017418, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00017358, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          242    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         1596  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         1963    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         1965    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         1967    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         1591    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         1590    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000202   0x08000202   0x00000004   Code   RO         1701    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000206   0x08000206   0x00000002   Code   RO         1838    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000208   0x08000208   0x00000004   Code   RO         1847    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1850    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1853    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1855    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1857    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1860    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1862    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1864    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1866    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1868    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1870    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1872    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1874    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1876    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1878    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1880    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1884    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1886    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1888    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000000   Code   RO         1890    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800020c   0x0800020c   0x00000002   Code   RO         1891    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000002   Code   RO         1917    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000210   0x08000210   0x00000000   Code   RO         1944    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000210   0x08000210   0x00000000   Code   RO         1946    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000210   0x08000210   0x00000000   Code   RO         1948    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000210   0x08000210   0x00000000   Code   RO         1951    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000210   0x08000210   0x00000000   Code   RO         1954    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000210   0x08000210   0x00000000   Code   RO         1956    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000210   0x08000210   0x00000000   Code   RO         1959    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000210   0x08000210   0x00000002   Code   RO         1960    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         1690    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000212   0x08000212   0x00000000   Code   RO         1785    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000212   0x08000212   0x00000006   Code   RO         1797    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000218   0x08000218   0x00000000   Code   RO         1787    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000218   0x08000218   0x00000004   Code   RO         1788    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800021c   0x0800021c   0x00000000   Code   RO         1790    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800021c   0x0800021c   0x00000008   Code   RO         1791    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000224   0x08000224   0x00000002   Code   RO         1839    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000226   0x08000226   0x00000000   Code   RO         1893    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000226   0x08000226   0x00000004   Code   RO         1894    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800022a   0x0800022a   0x00000006   Code   RO         1895    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000230   0x08000230   0x000000ec   Code   RO            3    .text               main.o
    0x0800031c   0x0800031c   0x0000001a   Code   RO          193    .text               stm32f4xx_it.o
    0x08000336   0x08000336   0x00000002   PAD
    0x08000338   0x08000338   0x00000210   Code   RO          216    .text               system_stm32f4xx.o
    0x08000548   0x08000548   0x00000040   Code   RO          243    .text               startup_stm32f40_41xxx.o
    0x08000588   0x08000588   0x000000e0   Code   RO          249    .text               misc.o
    0x08000668   0x08000668   0x00000464   Code   RO          295    .text               stm32f4xx_adc.o
    0x08000acc   0x08000acc   0x000003a8   Code   RO          518    .text               stm32f4xx_dma.o
    0x08000e74   0x08000e74   0x00000294   Code   RO          624    .text               stm32f4xx_gpio.o
    0x08001108   0x08001108   0x0000065c   Code   RO          787    .text               stm32f4xx_rcc.o
    0x08001764   0x08001764   0x00000ca2   Code   RO          932    .text               stm32f4xx_tim.o
    0x08002406   0x08002406   0x00000002   PAD
    0x08002408   0x08002408   0x00000454   Code   RO          952    .text               stm32f4xx_usart.o
    0x0800285c   0x0800285c   0x00000104   Code   RO          992    .text               delay.o
    0x08002960   0x08002960   0x00000578   Code   RO         1015    .text               sys.o
    0x08002ed8   0x08002ed8   0x0000015c   Code   RO         1041    .text               usart.o
    0x08003034   0x08003034   0x00000040   Code   RO         1095    .text               led.o
    0x08003074   0x08003074   0x000000d8   Code   RO         1141    .text               timer.o
    0x0800314c   0x0800314c   0x00000184   Code   RO         1165    .text               kalman.o
    0x080032d0   0x080032d0   0x00000768   Code   RO         1189    .text               adc.o
    0x08003a38   0x08003a38   0x0000074c   Code   RO         1231    .text               fft.o
    0x08004184   0x08004184   0x0000145c   Code   RO         1262    .text               lcd.o
    0x080055e0   0x080055e0   0x000028a8   Code   RO         1300    .text               lcd_ex.o
    0x08007e88   0x08007e88   0x00000ea4   Code   RO         1320    .text               ad9959_new.o
    0x08008d2c   0x08008d2c   0x000000f0   Code   RO         1379    .text               arm_cortexM4lf_math.lib(arm_cmplx_mag_f32.o)
    0x08008e1c   0x08008e1c   0x000006a0   Code   RO         1424    .text               arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x080094bc   0x080094bc   0x000000c0   Code   RO         1444    .text               arm_cortexM4lf_math.lib(arm_cfft_radix4_init_f32.o)
    0x0800957c   0x0800957c   0x000001e6   Code   RO         1468    .text               arm_cortexM4lf_math.lib(arm_bitreversal.o)
    0x08009762   0x08009762   0x00000002   Code   RO         1539    .text               c_w.l(use_no_semi_2.o)
    0x08009764   0x08009764   0x000000ee   Code   RO         1541    .text               c_w.l(lludivv7m.o)
    0x08009852   0x08009852   0x00000002   PAD
    0x08009854   0x08009854   0x00000018   Code   RO         1545    .text               c_w.l(noretval__2printf.o)
    0x0800986c   0x0800986c   0x00000068   Code   RO         1547    .text               c_w.l(__printf.o)
    0x080098d4   0x080098d4   0x00000058   Code   RO         1552    .text               c_w.l(_printf_hex_int.o)
    0x0800992c   0x0800992c   0x00000064   Code   RO         1592    .text               c_w.l(rt_memcpy_w.o)
    0x08009990   0x08009990   0x00000006   Code   RO         1594    .text               c_w.l(heapauxi.o)
    0x08009996   0x08009996   0x00000002   Code   RO         1688    .text               c_w.l(use_no_semi.o)
    0x08009998   0x08009998   0x00000016   Code   RO         1695    .text               c_w.l(_rserrno.o)
    0x080099ae   0x080099ae   0x000000b2   Code   RO         1697    .text               c_w.l(_printf_intcommon.o)
    0x08009a60   0x08009a60   0x00000024   Code   RO         1699    .text               c_w.l(_printf_char_file.o)
    0x08009a84   0x08009a84   0x00000008   Code   RO         1804    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08009a8c   0x08009a8c   0x00000030   Code   RO         1806    .text               c_w.l(_printf_char_common.o)
    0x08009abc   0x08009abc   0x00000008   Code   RO         1808    .text               c_w.l(ferror.o)
    0x08009ac4   0x08009ac4   0x00000008   Code   RO         1820    .text               c_w.l(libspace.o)
    0x08009acc   0x08009acc   0x0000004a   Code   RO         1823    .text               c_w.l(sys_stackheap_outer.o)
    0x08009b16   0x08009b16   0x00000012   Code   RO         1827    .text               c_w.l(exit.o)
    0x08009b28   0x08009b28   0x00000030   Code   RO         1816    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08009b58   0x08009b58   0x000002d8   Code   RO         1721    i.__hardfp_atan     m_wm.l(atan.o)
    0x08009e30   0x08009e30   0x00000200   Code   RO         1628    i.__hardfp_atan2    m_wm.l(atan2.o)
    0x0800a030   0x0800a030   0x000000c8   Code   RO         1640    i.__hardfp_cos      m_wm.l(cos.o)
    0x0800a0f8   0x0800a0f8   0x000000b0   Code   RO         1652    i.__hardfp_fmodf    m_wm.l(fmodf.o)
    0x0800a1a8   0x0800a1a8   0x000000c8   Code   RO         1664    i.__hardfp_sin      m_wm.l(sin.o)
    0x0800a270   0x0800a270   0x0000007a   Code   RO         1676    i.__hardfp_sqrt     m_wm.l(sqrt.o)
    0x0800a2ea   0x0800a2ea   0x00000006   PAD
    0x0800a2f0   0x0800a2f0   0x00000438   Code   RO         1773    i.__ieee754_rem_pio2  m_wm.l(rred.o)
    0x0800a728   0x0800a728   0x00000170   Code   RO         1735    i.__kernel_cos      m_wm.l(cos_i.o)
    0x0800a898   0x0800a898   0x000000f8   Code   RO         1818    i.__kernel_poly     m_wm.l(poly.o)
    0x0800a990   0x0800a990   0x00000130   Code   RO         1778    i.__kernel_sin      m_wm.l(sin_i.o)
    0x0800aac0   0x0800aac0   0x00000014   Code   RO         1739    i.__mathlib_dbl_infnan  m_wm.l(dunder.o)
    0x0800aad4   0x0800aad4   0x00000014   Code   RO         1740    i.__mathlib_dbl_infnan2  m_wm.l(dunder.o)
    0x0800aae8   0x0800aae8   0x00000020   Code   RO         1741    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x0800ab08   0x0800ab08   0x00000020   Code   RO         1744    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x0800ab28   0x0800ab28   0x00000010   Code   RO         1761    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x0800ab38   0x0800ab38   0x00000010   Code   RO         1723    i.atan              m_wm.l(atan.o)
    0x0800ab48   0x0800ab48   0x00000018   Code   RO         1754    i.fabs              m_wm.l(fabs.o)
    0x0800ab60   0x0800ab60   0x00000018   Code   RO         1702    x$fpl$basic         fz_wm.l(basic.o)
    0x0800ab78   0x0800ab78   0x00000062   Code   RO         1598    x$fpl$d2f           fz_wm.l(d2f.o)
    0x0800abda   0x0800abda   0x00000002   PAD
    0x0800abdc   0x0800abdc   0x00000150   Code   RO         1600    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x0800ad2c   0x0800ad2c   0x00000018   Code   RO         1704    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x0800ad44   0x0800ad44   0x000002b0   Code   RO         1607    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x0800aff4   0x0800aff4   0x00000078   Code   RO         1706    x$fpl$deqf          fz_wm.l(deqf.o)
    0x0800b06c   0x0800b06c   0x0000005e   Code   RO         1812    x$fpl$dfix          fz_wm.l(dfix.o)
    0x0800b0ca   0x0800b0ca   0x00000002   PAD
    0x0800b0cc   0x0800b0cc   0x0000005a   Code   RO         1610    x$fpl$dfixu         fz_wm.l(dfixu.o)
    0x0800b126   0x0800b126   0x0000002e   Code   RO         1615    x$fpl$dflt          fz_wm.l(dflt_clz.o)
    0x0800b154   0x0800b154   0x00000026   Code   RO         1614    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x0800b17a   0x0800b17a   0x00000002   PAD
    0x0800b17c   0x0800b17c   0x00000078   Code   RO         1620    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x0800b1f4   0x0800b1f4   0x00000154   Code   RO         1622    x$fpl$dmul          fz_wm.l(dmul.o)
    0x0800b348   0x0800b348   0x0000009c   Code   RO         1708    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x0800b3e4   0x0800b3e4   0x0000000c   Code   RO         1710    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x0800b3f0   0x0800b3f0   0x0000006c   Code   RO         1624    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x0800b45c   0x0800b45c   0x00000016   Code   RO         1601    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x0800b472   0x0800b472   0x00000002   PAD
    0x0800b474   0x0800b474   0x00000198   Code   RO         1712    x$fpl$dsqrt         fz_wm.l(dsqrt_umaal.o)
    0x0800b60c   0x0800b60c   0x000001d4   Code   RO         1602    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x0800b7e0   0x0800b7e0   0x00000056   Code   RO         1626    x$fpl$f2d           fz_wm.l(f2d.o)
    0x0800b836   0x0800b836   0x0000008c   Code   RO         1714    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x0800b8c2   0x0800b8c2   0x0000000a   Code   RO         1905    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x0800b8cc   0x0800b8cc   0x000000f4   Code   RO         1716    x$fpl$frem          fz_wm.l(frem_clz.o)
    0x0800b9c0   0x0800b9c0   0x0000000a   Code   RO         1718    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x0800b9ca   0x0800b9ca   0x00000000   Code   RO         1720    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x0800b9ca   0x0800b9ca   0x00002f80   Data   RO         1264    .constdata          lcd.o
    0x0800e94a   0x0800e94a   0x00000800   Data   RO         1488    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800f14a   0x0800f14a   0x00000002   PAD
    0x0800f14c   0x0800f14c   0x00008000   Data   RO         1497    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0801714c   0x0801714c   0x00000028   Data   RO         1553    .constdata          c_w.l(_printf_hex_int.o)
    0x08017174   0x08017174   0x00000004   PAD
    0x08017178   0x08017178   0x00000098   Data   RO         1724    .constdata          m_wm.l(atan.o)
    0x08017210   0x08017210   0x00000030   Data   RO         1736    .constdata          m_wm.l(cos_i.o)
    0x08017240   0x08017240   0x00000008   Data   RO         1772    .constdata          m_wm.l(qnan.o)
    0x08017248   0x08017248   0x000000c8   Data   RO         1775    .constdata          m_wm.l(rred.o)
    0x08017310   0x08017310   0x00000028   Data   RO         1779    .constdata          m_wm.l(sin_i.o)
    0x08017338   0x08017338   0x00000020   Data   RO         1961    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08017358, Size: 0x000169e0, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08017358   0x00000014   Data   RW          217    .data               system_stm32f4xx.o
    0x20000014   0x0801736c   0x00000010   Data   RW          788    .data               stm32f4xx_rcc.o
    0x20000024   0x0801737c   0x00000004   Data   RW          993    .data               delay.o
    0x20000028   0x08017380   0x0000000a   Data   RW         1043    .data               usart.o
    0x20000032   0x0801738a   0x00000002   PAD
    0x20000034   0x0801738c   0x00000004   Data   RW         1142    .data               timer.o
    0x20000038   0x08017390   0x00000004   Data   RW         1167    .data               kalman.o
    0x2000003c   0x08017394   0x00000027   Data   RW         1191    .data               adc.o
    0x20000063   0x080173bb   0x00000001   PAD
    0x20000064   0x080173bc   0x0000003c   Data   RW         1233    .data               fft.o
    0x200000a0   0x080173f8   0x00000008   Data   RW         1265    .data               lcd.o
    0x200000a8   0x08017400   0x00000018   Data   RW         1322    .data               ad9959_new.o
    0x200000c0        -       0x000000c8   Zero   RW         1042    .bss                usart.o
    0x20000188        -       0x00000134   Zero   RW         1166    .bss                kalman.o
    0x200002bc        -       0x00006000   Zero   RW         1190    .bss                adc.o
    0x200062bc        -       0x00010090   Zero   RW         1232    .bss                fft.o
    0x2001634c        -       0x0000000e   Zero   RW         1263    .bss                lcd.o
    0x2001635a   0x08017418   0x00000002   PAD
    0x2001635c        -       0x00000020   Zero   RW         1321    .bss                ad9959_new.o
    0x2001637c        -       0x00000060   Zero   RW         1821    .bss                c_w.l(libspace.o)
    0x200163dc   0x08017418   0x00000004   PAD
    0x200163e0        -       0x00000200   Zero   RW          241    HEAP                startup_stm32f40_41xxx.o
    0x200165e0        -       0x00000400   Zero   RW          240    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      3748        198          0         24         32      12881   ad9959_new.o
      1896        124          0         39      24576       4671   adc.o
       260          8          0          4          0       1589   delay.o
      1868        156          0         60      65680       6139   fft.o
       388         38          0          4        308       2477   kalman.o
      5212        140      12160          8         14      15397   lcd.o
     10408          0          0          0          0       7233   lcd_ex.o
        64          4          0          0          0        579   led.o
       236         34          0          0          0     307421   main.o
       224         20          0          0          0     245061   misc.o
        64         26        392          0       1536        888   startup_stm32f40_41xxx.o
      1124         24          0          0          0      10794   stm32f4xx_adc.o
       936         32          0          0          0       6501   stm32f4xx_dma.o
       660         44          0          0          0       4265   stm32f4xx_gpio.o
        26          0          0          0          0       1274   stm32f4xx_it.o
      1628         52          0         16          0      13196   stm32f4xx_rcc.o
      3234         60          0          0          0      23120   stm32f4xx_tim.o
      1108         34          0          0          0       7988   stm32f4xx_usart.o
      1400         42          0          0          0       6791   sys.o
       528         46          0         20          0       1939   system_stm32f4xx.o
       216         20          0          4          0       1443   timer.o
       348         20          0         10        200       3450   usart.o

    ----------------------------------------------------------------------
     35580       <USER>      <GROUP>        192      92348     685097   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         4          0          0          3          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       486          0          0          0          0       2210   arm_bitreversal.o
      1696          0          0          0          0       4744   arm_cfft_radix4_f32.o
       192         46          0          0          0        767   arm_cfft_radix4_init_f32.o
       240          4          0          0          0      16312   arm_cmplx_mag_f32.o
         0          0      34816          0          0       2517   arm_common_tables.o
         8          0          0          0          0         68   __main.o
       104          0          0          0          0         84   __printf.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
        88          4         40          0          0         88   _printf_hex_int.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       238          0          0          0          0        100   lludivv7m.o
        24          4          0          0          0         84   noretval__2printf.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        24          0          0          0          0        164   basic.o
        98          4          0          0          0        140   d2f.o
       826         16          0          0          0        492   daddsub_clz.o
        24          0          0          0          0        116   dcmpi.o
       688        140          0          0          0        256   ddiv.o
       120          4          0          0          0        140   deqf.o
        94          4          0          0          0        140   dfix.o
        90          4          0          0          0        140   dfixu.o
        84          0          0          0          0        232   dflt_clz.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
       408         56          0          0          0        168   dsqrt_umaal.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
       244          8          0          0          0        132   frem_clz.o
        10          0          0          0          0        116   fretinf.o
         0          0          0          0          0          0   usenofp.o
       744        106        152          0          0        352   atan.o
       512         64          0          0          0        208   atan2.o
       200         20          0          0          0        164   cos.o
       368         46         48          0          0        200   cos_i.o
       104         16          0          0          0        496   dunder.o
        24          0          0          0          0        124   fabs.o
       176          0          0          0          0        168   fmodf.o
        48          0          0          0          0        124   fpclassify.o
        16          6          0          0          0        116   funder.o
       248          0          0          0          0        152   poly.o
         0          0          8          0          0          0   qnan.o
      1080        142        200          0          0        188   rred.o
       200         20          0          0          0        164   sin.o
       304         24         40          0          0        208   sin_i.o
       122          0          0          0          0        148   sqrt.o

    ----------------------------------------------------------------------
     11590        <USER>      <GROUP>          0        100      34174   Library Totals
        18          0          6          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2614         50      34816          0          0      26550   arm_cortexM4lf_math.lib
      1130         34         40          0         96       1520   c_w.l
      3682        264          0          0          0       3292   fz_wm.l
      4146        444        448          0          0       2812   m_wm.l

    ----------------------------------------------------------------------
     11590        <USER>      <GROUP>          0        100      34174   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     47170       1914      47894        192      92448     709343   Grand Totals
     47170       1914      47894        192      92448     709343   ELF Image Totals
     47170       1914      47894        192          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                95064 (  92.84kB)
    Total RW  Size (RW Data + ZI Data)             92640 (  90.47kB)
    Total ROM Size (Code + RO Data + RW Data)      95256 (  93.02kB)

==============================================================================

