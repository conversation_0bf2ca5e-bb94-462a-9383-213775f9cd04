#include "stm32f4xx.h"
#include "stdio.h"
#include "usart.h"
#include "delay.h"
#include "led.h"
#include "G.h"
#include "timer.h"
#include "math.h"
#include "arm_math.h"
#include "kalman.h"
#include "fft.h"
#include "adc.h"
#include "AD9959_NEW.h"
#include "lcd.h"
#include <stdbool.h>
#include <string.h>
#include <stdlib.h>

// Function prototypes match the declarations in adc.h
void QCZ_FFT(volatile uint16_t* buff);
void QCZ_FFT1(volatile uint16_t* buff);

// Global variables from your project
bool Separate = false;
extern int t;
extern float fft_outputbuf[FFT_LENGTH];
extern u8 Res;
uint32_t frequency_A, frequency_B;
int phase_difference_A;
int phase_difference_B;
int phase_difference_A1;
int phase_difference_B1;
extern float phase_A, phase_B, phase;
extern float frequency;
double current_output_freq_A, current_output_freq_B;
float phase_A_CS = 0.0f;
float phase_B_CS = 0.0f;
float phase_A_SX = 0.0f;
float phase_B_SX = 0.0f;
uint16_t current_phase_B = 0;
uint32_t peak_idx;
extern uint32_t peak1_idx, peak2_idx;

// Extern declarations now match the original types in your header files
extern volatile uint16_t buff_adc[];
extern volatile uint16_t buff_adc2[];
extern volatile uint16_t buff_adc3[];

// CORRECTED: Declarations are now on separate lines to match adc.h
extern volatile u8 flag_ADC;
extern volatile u8 flag_ADC1;
extern volatile u8 flag_ADC2;

extern float sampfre;
extern arm_cfft_radix4_instance_f32 scfft;

u8 QCZ = 100;
u8 QCZ1 = 0;
int QCZ_Phase[2];
int QCZ_Phase1[2];
int Phase = 0;
float ZE;
int SBP = 0;

uint16_t waveform_A, waveform_B;
uint16_t waveform_A_prime, waveform_B_prime;

char lcd_buffer[50];





int main(void)
{
    arm_cfft_radix4_init_f32(&scfft, FFT_LENGTH, 0, 1);
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    uart_init(112500);
    delay_init(168);
    LED_Init();
    Adc_Init();
    Adc2_Init();
    Adc3_Init();
    DMA1_Init();
    DMA2_Init();
    DMA3_Init();
    // AD9833_Init();  // 注释掉，因为使用的是AD9959
    // AD9833_Init1(); // 注释掉，因为使用的是AD9959
    Init_AD9959();     // 初始化AD9959

    lcd_init();

    sampfre = 409756;

    TIM3_Int_Init(5 - 1, 41 - 1);
    TIM4_Int_Init(1000 - 1, 8400 - 1);
    TIM_Cmd(TIM3, ENABLE);

    // UI Redesign for better aesthetics and clarity
    lcd_clear(WHITE);
    g_point_color = BLACK;

    const uint8_t FONT_SIZE = 16;
    const uint8_t LINE_HEIGHT = FONT_SIZE + 6;
    const uint16_t MARGIN = 10;
    const uint16_t PANEL_WIDTH = lcddev.width - 2 * MARGIN;

    const uint16_t COL1_X = 10;
    const uint16_t COL2_X = 70;
    const uint16_t COL3_X = 200;

    uint16_t y_pos = MARGIN;


    // Initialize AD9959
    Init_AD9959();
    
    // Channel 1: 100Hz sine wave with full amplitude (1023)
    WriteFreq(0, 100, 1);  // Channel 0, 100Hz, immediate update
    WriteAmplitude(0, 1023, 1);  // Full amplitude
    
    // Channel 2: Frequency sweep with half amplitude
    // Sweep from 1kHz to 10kHz in 100Hz steps, 10ms dwell time per step
    WriteAmplitude(1, 512, 1);  // Half amplitude (512 = 1023/2)
    AD9959FreqSweep(1000, 10000, 100, 10, LINEAR, 1);  // Channel 1 sweep
    
    while (1)
    {
        // Main loop can be used for monitoring or UI updates
    }
}
