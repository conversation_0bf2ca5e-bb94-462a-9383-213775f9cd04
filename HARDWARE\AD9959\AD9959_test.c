/*---------------------------------------------------------
AD9959测试程序
author: XHMM
file: AD9959_test.c
version: v1.0
Date: 2025.7.30
state: 标准库版本
----------------------------------------------------------*/

#include "AD9959_NEW.h"
#include "delay.h"
#include "usart.h"

/**
 * @brief AD9959基本功能测试
 */
void AD9959_BasicTest(void)
{
    printf("AD9959 Basic Test Start...\r\n");
    
    // 初始化AD9959
    Init_AD9959();
    printf("AD9959 Initialized\r\n");
    
    // 测试通道0 - 设置30MHz频率
    WriteFreq(0, 30*MHz, 1);
    printf("Channel 0: 30MHz set\r\n");
    delay_ms(1000);
    
    // 测试通道1 - 设置40MHz频率
    WriteFreq(1, 40*MHz, 1);
    printf("Channel 1: 40MHz set\r\n");
    delay_ms(1000);
    
    // 测试通道2 - 设置50MHz频率
    WriteFreq(2, 50*MHz, 1);
    printf("Channel 2: 50MHz set\r\n");
    delay_ms(1000);
    
    // 测试通道3 - 设置60MHz频率
    WriteFreq(3, 60*MHz, 1);
    printf("Channel 3: 60MHz set\r\n");
    delay_ms(1000);
    
    printf("Basic frequency test completed\r\n");
}

/**
 * @brief AD9959幅度测试
 */
void AD9959_AmplitudeTest(void)
{
    printf("AD9959 Amplitude Test Start...\r\n");
    
    // 设置通道0为30MHz，测试不同幅度
    WriteFreq(0, 30*MHz, 0);
    
    // 最大幅度
    WriteAmplitude(0, 1023, 1);
    printf("Channel 0: Max amplitude (1023)\r\n");
    delay_ms(2000);
    
    // 中等幅度
    WriteAmplitude(0, 512, 1);
    printf("Channel 0: Medium amplitude (512)\r\n");
    delay_ms(2000);
    
    // 最小幅度
    WriteAmplitude(0, 100, 1);
    printf("Channel 0: Min amplitude (100)\r\n");
    delay_ms(2000);
    
    // 恢复最大幅度
    WriteAmplitude(0, 1023, 1);
    printf("Channel 0: Amplitude restored to max\r\n");
    
    printf("Amplitude test completed\r\n");
}

/**
 * @brief AD9959相位测试
 */
void AD9959_PhaseTest(void)
{
    printf("AD9959 Phase Test Start...\r\n");
    
    // 设置通道0和通道1为相同频率
    WriteFreq(0, 30*MHz, 0);
    WriteFreq(1, 30*MHz, 0);
    
    // 测试不同相位差
    WritePhase(0, 0, 0);        // 0度
    WritePhase(1, 4096, 1);     // 90度
    printf("Phase difference: 90 degrees\r\n");
    delay_ms(2000);
    
    WritePhase(1, 8192, 1);     // 180度
    printf("Phase difference: 180 degrees\r\n");
    delay_ms(2000);
    
    WritePhase(1, 12288, 1);    // 270度
    printf("Phase difference: 270 degrees\r\n");
    delay_ms(2000);
    
    WritePhase(1, 0, 1);        // 0度
    printf("Phase difference: 0 degrees\r\n");
    
    printf("Phase test completed\r\n");
}

/**
 * @brief AD9959正交输出测试
 */
void AD9959_QuadratureTest(void)
{
    printf("AD9959 Quadrature Test Start...\r\n");
    
    // 设置四路正交输出（相位差90度）
    uint32_t freq = 50*MHz;
    WriteFreqOrtho(freq);
    printf("Quadrature output at %d MHz set\r\n", freq/MHz);
    
    delay_ms(5000);
    
    printf("Quadrature test completed\r\n");
}

/**
 * @brief AD9959扫频测试
 */
void AD9959_SweepTest(void)
{
    printf("AD9959 Sweep Test Start...\r\n");
    
    // 线性扫频测试：从10MHz到100MHz，步进1MHz，停留时间100ns
    uint32_t startFreq = 10*MHz;
    uint32_t stopFreq = 100*MHz;
    uint32_t step = 1*MHz;
    uint32_t stayTime = 100;  // ns
    
    SweepFrequency(0, startFreq, stopFreq, step, stayTime, 0);
    printf("Linear sweep: %d MHz to %d MHz, step %d MHz\r\n", 
           startFreq/MHz, stopFreq/MHz, step/MHz);
    
    delay_ms(10000);  // 等待扫频完成
    
    printf("Sweep test completed\r\n");
}

/**
 * @brief 完整的AD9959测试程序
 */
void AD9959_FullTest(void)
{
    printf("\r\n=== AD9959 Full Test Suite ===\r\n");
    
    // 基本功能测试
    AD9959_BasicTest();
    delay_ms(2000);
    
    // 幅度测试
    AD9959_AmplitudeTest();
    delay_ms(2000);
    
    // 相位测试
    AD9959_PhaseTest();
    delay_ms(2000);
    
    // 正交输出测试
    AD9959_QuadratureTest();
    delay_ms(2000);
    
    // 扫频测试
    AD9959_SweepTest();
    
    printf("\r\n=== AD9959 Test Suite Completed ===\r\n");
}
