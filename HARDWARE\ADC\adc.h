#ifndef __ADC_H
#define __ADC_H	
#include "sys.h" 
#include "stm32f4xx.h"
#include "delay.h"
#include "fft.h"
#include "arm_math.h"

extern __IO uint16_t  buff_adc[];
extern __IO uint16_t  buff_adc2[];
extern __IO uint16_t  buff_adc3[];

extern volatile u8 flag_ADC;
extern volatile u8 flag_ADC1;
extern volatile u8 flag_ADC2;

void Adc_Init(void); 				//ADC通道初始化
void DMA1_Init(void);       //dma初始化
void DMA2_Init(void);
void Adc2_Init(void);
void Adc3_Init(void);
void DMA3_Init(void);

void QCZ_FFT(__IO uint16_t* BUFF_ADC);
void QCZ_FFT1(__IO uint16_t* BUFF_ADC);
    
#endif
