#include "adc.h"

#define ADC1_DR_ADDRESS          (uint32_t)(0x40012000+0x04C)
#define ADC2_DR_ADDRESS          (uint32_t)(0x40012100+0x04C)
#define ADC3_DR_ADDRESS          (uint32_t)((u32)ADC3+0x04C)

__IO uint16_t  buff_adc[FFT_LENGTH];
__IO uint16_t  buff_adc2[FFT_LENGTH];
__IO uint16_t  buff_adc3[FFT_LENGTH];

float Adresult1,Adresult2,Adresult3;
float frequency1,frequency2,frequency3;

float phase=0.0f;
float phase_A=0.0f;
float phase_B=0.0f;

void QCZ_FFT(__IO uint16_t* BUFF_ADC)
{ 
   int t;
   for(t=0; t<FFT_LENGTH; t++) //加窗    ADC 1，4096
        {
            BUFF_ADC[t]= BUFF_ADC[t]*Hanningwindow(t); 
        }
        FFT(BUFF_ADC);//fft
        //get_basefrevpp();//得到基波的峰峰与频率
        phase_A=atan2(fft_inputbuf[2*peak1_idx+1],fft_inputbuf[2*peak1_idx])*180/PI;
        phase_B=atan2(fft_inputbuf[2*peak2_idx+1],fft_inputbuf[2*peak2_idx])*180/PI;
}

void QCZ_FFT1(__IO uint16_t* BUFF_ADC)
{ 
   int t;
   for(t=0; t<FFT_LENGTH; t++) //加窗    ADC 1，4096
        {
            BUFF_ADC[t]= BUFF_ADC[t]*Hanningwindow(t); 
        }
        FFT(BUFF_ADC);//fft
        get_basefrevpp();//得到基波的峰峰与频率
        phase=atan2(fft_inputbuf[2*timef+1],fft_inputbuf[2*timef])*180/PI;
}

//初始化ADC
void  Adc_Init(void)
{
    ADC_CommonInitTypeDef ADC_CommonInitStructure;
    ADC_InitTypeDef       ADC_InitStructure;
    GPIO_InitTypeDef  GPIO_InitStructure;


    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);//使能GPIOA时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC1, ENABLE); //使能ADC1时钟

    //先初始化ADC1通道5 IO口
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_1;//PA1 通道5
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;//模拟输入
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_DOWN ;//不带上下拉
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure);//初始化

    RCC_APB2PeriphResetCmd(RCC_APB2Periph_ADC1,ENABLE);	  //ADC1复位
    RCC_APB2PeriphResetCmd(RCC_APB2Periph_ADC1,DISABLE);	//复位结束

    ADC_CommonInitStructure.ADC_Mode = ADC_Mode_Independent;//独立模式
// ADC_CommonInitStructure.ADC_TwoSamplingDelay = ADC_TwoSamplingDelay_5Cycles;//两个采样阶段之间的延迟5个时钟
    ADC_CommonInitStructure.ADC_DMAAccessMode = ADC_DMAAccessMode_Disabled; //DMA失能
    ADC_CommonInitStructure.ADC_Prescaler = ADC_Prescaler_Div4;//预分频4分频。ADCCLK=PCLK2/4=84/4=21Mhz,ADC时钟最好不要超过36Mhz
    ADC_CommonInit(&ADC_CommonInitStructure);//初始化

    ADC_InitStructure.ADC_Resolution = ADC_Resolution_12b;//12位模式
    ADC_InitStructure.ADC_ScanConvMode = DISABLE;//非扫描模式
    //ADC_InitStructure.ADC_ContinuousConvMode = ENABLE;//连续转换
    ADC_InitStructure.ADC_ContinuousConvMode =DISABLE;//关闭连续转换
    //ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_None;//禁止触发检测，使用软件触发
    ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_Rising;
    ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_T3_TRGO;
    ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;//右对齐
    ADC_InitStructure.ADC_NbrOfConversion = 1;//1个转换在规则序列中 也就是只转换规则序列1
    ADC_Init(ADC1, &ADC_InitStructure);//ADC初始化

    ADC_RegularChannelConfig(ADC1, ADC_Channel_1, 1, ADC_SampleTime_3Cycles );	//ADC1,ADC通道,480个周期,提高采样时间可以提高精确度


    ADC_DiscModeChannelCountConfig(ADC1,1);//为选定的ADC规则通道配置间断模式通道数目
    ADC_DiscModeCmd(ADC1,ENABLE);//为指定的ADC使能或者失能规则通道上的间断模式


//	ADC_SoftwareStartConv(ADC1);		//使能指定的ADC1的软件转换启动功能
    ADC_DMARequestAfterLastTransferCmd(ADC1, ENABLE);
    ADC_DMACmd(ADC1, ENABLE);
    ADC_Cmd(ADC1, ENABLE);//开启AD转换器
}

void DMA1_Init(void)
{
    DMA_InitTypeDef  DMA_InitStructure;//DMA初始化结构体
    NVIC_InitTypeDef NVIC_InitStructure;//NVIC 初始化结构体
    
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_1);//配置 NVIC 为优先级组 1
    NVIC_InitStructure.NVIC_IRQChannel= DMA2_Stream0_IRQn;//中断源
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;//抢占优先级
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 2;  //子优先级
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;//使能中断通道
    NVIC_Init(&NVIC_InitStructure);
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA2, ENABLE);//开启DMA2时钟
    DMA_DeInit(DMA2_Stream0);//初始化DMA数据流0
     
    while (DMA_GetCmdStatus(DMA2_Stream0) != DISABLE) //确保数据流复位完成
    {
    }
    DMA_InitStructure.DMA_Channel = DMA_Channel_0; // 通道选择
    
  
    DMA_InitStructure.DMA_PeripheralBaseAddr = ADC1_DR_ADDRESS;//外设的数据寄存器地址
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)buff_adc;//数组名表示首地址
 
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralToMemory;//传输方向
    DMA_InitStructure.DMA_BufferSize = FFT_LENGTH;//数据数目
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;//不开启外设递增
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;//存储器递增
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;//外设数据宽度 半字（16个比特位）
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;//存储器数据宽度
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;//DMA传输模式选择，可选一次传输或者循环传输，使用普通模式
    //传输完成后等待数据处理完成后在主程序里重先开启dma
    //这样做的目的是防止循环模式中数据还没处理完就被新的数据覆盖
    DMA_InitStructure.DMA_Priority = DMA_Priority_High;//软件设置数据流的优先级
  
    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable; //FIFO 模式使能  关闭
   
    DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_HalfFull;//FIFO 阈值选择
    DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;//存储器突发模式选择 ADC 采集传输是直接模式，要求使用单次模式
    DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;//外设突发模式选择 ADC 采集传输是直接模式，要求使用单次模式
    DMA_Init(DMA2_Stream0, &DMA_InitStructure);

    DMA_ClearITPendingBit(DMA2_Stream0,DMA_IT_TCIF0);//清除中断标志位
    DMA_ITConfig(DMA2_Stream0,DMA_IT_TC, ENABLE);//开启dma2数据流0的传输完成中断
    DMA_Cmd(DMA2_Stream0, ENABLE);//使能dma2数据流0，开始dma传输
    while (DMA_GetCmdStatus(DMA2_Stream0) != ENABLE)
    {
    }

}

volatile u8 flag_ADC;
void DMA2_Stream0_IRQHandler(void)         // 使用DMA中断采集数据，不会容易丢失数据
{
    if(DMA_GetITStatus(DMA2_Stream0,DMA_IT_TCIF0)!=RESET)
    {
        
        flag_ADC=1;
        TIM_Cmd(TIM3, DISABLE);
        DMA_ClearITPendingBit(DMA2_Stream0,DMA_IT_TCIF0);          
        //printf("1\r\n");
        
     }
}

void  Adc2_Init(void)
{
    ADC_CommonInitTypeDef ADC_CommonInitStructure;
    ADC_InitTypeDef       ADC_InitStructure;
    GPIO_InitTypeDef  GPIO_InitStructure;


    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);//使能GPIOA时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC2, ENABLE); //使能ADC2时钟

    //先初始化ADC1通道5 IO口
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4;//PA4 通道7
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;//模拟输入
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_DOWN ;//不带上下拉
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure);//初始化

    RCC_APB2PeriphResetCmd(RCC_APB2Periph_ADC2,ENABLE);	  //ADC2复位
    RCC_APB2PeriphResetCmd(RCC_APB2Periph_ADC2,DISABLE);	//复位结束


    ADC_CommonInitStructure.ADC_Mode = ADC_Mode_Independent;//独立模式
// ADC_CommonInitStructure.ADC_TwoSamplingDelay = ADC_TwoSamplingDelay_5Cycles;//两个采样阶段之间的延迟5个时钟
    ADC_CommonInitStructure.ADC_DMAAccessMode = ADC_DMAAccessMode_Disabled; //DMA失能
    ADC_CommonInitStructure.ADC_Prescaler = ADC_Prescaler_Div4;//预分频4分频。ADCCLK=PCLK2/4=84/4=21Mhz,ADC时钟最好不要超过36Mhz
    ADC_CommonInit(&ADC_CommonInitStructure);//初始化

    ADC_InitStructure.ADC_Resolution = ADC_Resolution_12b;//12位模式
    ADC_InitStructure.ADC_ScanConvMode = DISABLE;//非扫描模式
    //ADC_InitStructure.ADC_ContinuousConvMode = ENABLE;//连续转换
    ADC_InitStructure.ADC_ContinuousConvMode =DISABLE;//关闭连续转换
    //ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_None;//禁止触发检测，使用软件触发
    ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_Rising;//上升沿触发（应该可以去掉）
    ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_T3_TRGO;//（外部触发模式选择）
    ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;//右对齐
    ADC_InitStructure.ADC_NbrOfConversion = 1;//1个转换在规则序列中 也就是只转换规则序列1
    ADC_Init(ADC2, &ADC_InitStructure);//ADC初始化

    ADC_RegularChannelConfig(ADC2, ADC_Channel_4, 1, ADC_SampleTime_3Cycles );	//ADC1,ADC通道,480个周期,提高采样时间可以提高精确度


    ADC_DiscModeChannelCountConfig(ADC2,2);
    ADC_DiscModeCmd(ADC2,ENABLE);


//	ADC_SoftwareStartConv(ADC1);		//使能指定的ADC1的软件转换启动功能

    ADC_DMARequestAfterLastTransferCmd(ADC2, ENABLE);
    ADC_DMACmd(ADC2, ENABLE);
    ADC_Cmd(ADC2, ENABLE);//开启AD转换器
}

void DMA2_Init(void)
{
    DMA_InitTypeDef  DMA_InitStructure;//DMA初始化结构体
    NVIC_InitTypeDef NVIC_InitStructure;//NVIC 初始化结构体
    
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_1);//配置 NVIC 为优先级组 1
    NVIC_InitStructure.NVIC_IRQChannel= DMA2_Stream2_IRQn;//中断源
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;//抢占优先级
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 2;  //子优先级
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;//使能中断通道
    NVIC_Init(&NVIC_InitStructure);
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA2, ENABLE);//开启DMA2时钟
    DMA_DeInit(DMA2_Stream2);//初始化DMA数据流0
    
    while (DMA_GetCmdStatus(DMA2_Stream2) != DISABLE) //确保数据流复位完成
    {
    }
    DMA_InitStructure.DMA_Channel = DMA_Channel_1; // 通道选择
  
    DMA_InitStructure.DMA_PeripheralBaseAddr = ADC2_DR_ADDRESS;//外设的数据寄存器地址
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)buff_adc2;//数组名表示首地址
   
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralToMemory;//传输方向
    DMA_InitStructure.DMA_BufferSize = FFT_LENGTH;//数据数目
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;//不开启外设递增
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;//存储器递增
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;//外设数据宽度 半字（16个比特位）
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;//存储器数据宽度
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;//DMA传输模式选择，可选一次传输或者循环传输，使用普通模式
    //传输完成后等待数据处理完成后在主程序里重先开启dma
    //这样做的目的是防止循环模式中数据还没处理完就被新的数据覆盖
    DMA_InitStructure.DMA_Priority = DMA_Priority_High;//软件设置数据流的优先级

    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable; //FIFO 模式使能  关闭
  
    DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_HalfFull;//FIFO 阈值选择
    DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;//存储器突发模式选择 ADC 采集传输是直接模式，要求使用单次模式
    DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;//外设突发模式选择 ADC 采集传输是直接模式，要求使用单次模式
    DMA_Init(DMA2_Stream2, &DMA_InitStructure);

    DMA_ClearITPendingBit(DMA2_Stream2,DMA_IT_TCIF2);//清除中断标志位
    DMA_ITConfig(DMA2_Stream2,DMA_IT_TC, ENABLE);//开启dma2数据流2的传输完成中断
    DMA_Cmd(DMA2_Stream2, ENABLE);//使能dma2数据流0，开始dma传输
    while (DMA_GetCmdStatus(DMA2_Stream2) != ENABLE)
    {
    }

}

volatile u8 flag_ADC1;
void DMA2_Stream2_IRQHandler(void)         // 使用DMA中断采集数据，不会容易丢失数据
{
    if(DMA_GetITStatus(DMA2_Stream2,DMA_IT_TCIF2)!=RESET)
    {
        
        flag_ADC1=1;
        TIM_Cmd(TIM3, DISABLE);
        DMA_ClearITPendingBit(DMA2_Stream2,DMA_IT_TCIF2);          
        //printf("2\r\n"); 
    }
}

void  Adc3_Init(void)
{
    ADC_CommonInitTypeDef ADC_CommonInitStructure;
    ADC_InitTypeDef       ADC_InitStructure;
    GPIO_InitTypeDef  GPIO_InitStructure;

    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOF, ENABLE);//使能GPIOA时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC3, ENABLE); //使能ADC3时钟

    //先初始化ADC1通道3 IO口
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_7;//PF7 通道5
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;//模拟输入
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_DOWN ;//不带上下拉
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_Init(GPIOF, &GPIO_InitStructure);//初始化

    RCC_APB2PeriphResetCmd(RCC_APB2Periph_ADC3,ENABLE);	  //ADC3复位
    RCC_APB2PeriphResetCmd(RCC_APB2Periph_ADC3,DISABLE);	//复位结束


    ADC_CommonInitStructure.ADC_Mode = ADC_Mode_Independent;//独立模式
// ADC_CommonInitStructure.ADC_TwoSamplingDelay = ADC_TwoSamplingDelay_5Cycles;//两个采样阶段之间的延迟5个时钟
    ADC_CommonInitStructure.ADC_DMAAccessMode = ADC_DMAAccessMode_Disabled; //DMA失能
    ADC_CommonInitStructure.ADC_Prescaler = ADC_Prescaler_Div4;//预分频4分频。ADCCLK=PCLK2/4=84/4=21Mhz,ADC时钟最好不要超过36Mhz
    ADC_CommonInit(&ADC_CommonInitStructure);//初始化

    ADC_InitStructure.ADC_Resolution = ADC_Resolution_12b;//12位模式
    ADC_InitStructure.ADC_ScanConvMode = DISABLE;//非扫描模式
    //ADC_InitStructure.ADC_ContinuousConvMode = ENABLE;//连续转换
    ADC_InitStructure.ADC_ContinuousConvMode =DISABLE;//关闭连续转换
    //ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_None;//禁止触发检测，使用软件触发
    ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_Rising;
    ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_T3_TRGO;
    ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;//右对齐
    ADC_InitStructure.ADC_NbrOfConversion = 1;//1个转换在规则序列中 也就是只转换规则序列1
    ADC_Init(ADC3, &ADC_InitStructure);//ADC初始化

    ADC_RegularChannelConfig(ADC3, ADC_Channel_5, 1, ADC_SampleTime_3Cycles );	//ADC3,ADC通道,480个周期,提高采样时间可以提高精确度


    ADC_DiscModeChannelCountConfig(ADC3,3);
    ADC_DiscModeCmd(ADC3,ENABLE);


//	ADC_SoftwareStartConv(ADC1);		//使能指定的ADC1的软件转换启动功能

    ADC_DMARequestAfterLastTransferCmd(ADC3, ENABLE);
    ADC_DMACmd(ADC3, ENABLE);
    ADC_Cmd(ADC3, ENABLE);//开启AD转换器
}

void DMA3_Init(void)
{
    DMA_InitTypeDef  DMA_InitStructure;//DMA初始化结构体
    NVIC_InitTypeDef NVIC_InitStructure;//NVIC 初始化结构体
    
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_1);//配置 NVIC 为优先级组 1
    NVIC_InitStructure.NVIC_IRQChannel= DMA2_Stream1_IRQn;//中断源
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;//抢占优先级
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 2;  //子优先级
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;//使能中断通道
    NVIC_Init(&NVIC_InitStructure);
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA2, ENABLE);//开启DMA2时钟
    DMA_DeInit(DMA2_Stream1);//初始化DMA数据流0

     while (DMA_GetCmdStatus(DMA2_Stream1) != DISABLE) //确保数据流复位完成
    {
    }
    DMA_InitStructure.DMA_Channel = DMA_Channel_2; // 通道选择
    DMA_InitStructure.DMA_PeripheralBaseAddr = ADC3_DR_ADDRESS;//外设的数据寄存器地址
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)buff_adc3;//数组名表示首地址
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralToMemory;//传输方向
    DMA_InitStructure.DMA_BufferSize = FFT_LENGTH;//数据数目
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;//不开启外设递增
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;//存储器递增
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;//外设数据宽度 半字（16个比特位）
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;//存储器数据宽度
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;//DMA传输模式选择，可选一次传输或者循环传输，使用普通模式
    //传输完成后等待数据处理完成后在主程序里重先开启dma
    //这样做的目的是防止循环模式中数据还没处理完就被新的数据覆盖
    DMA_InitStructure.DMA_Priority = DMA_Priority_High;//软件设置数据流的优先级
    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable; //FIFO 模式使能  关闭
    DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_HalfFull;//FIFO 阈值选择
    DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;//存储器突发模式选择 ADC 采集传输是直接模式，要求使用单次模式
    DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;//外设突发模式选择 ADC 采集传输是直接模式，要求使用单次模式
    DMA_Init(DMA2_Stream1, &DMA_InitStructure);

    DMA_ClearITPendingBit(DMA2_Stream1,DMA_IT_TCIF1);//清除中断标志位
    DMA_ITConfig(DMA2_Stream1,DMA_IT_TC, ENABLE);//开启dma1数据流0的传输完成中断
    DMA_Cmd(DMA2_Stream1, ENABLE);//使能dma2数据流0，开始dma传输
    while (DMA_GetCmdStatus(DMA2_Stream1) != ENABLE)
    {
    }
    
}

volatile u8 flag_ADC2;
void DMA2_Stream1_IRQHandler(void)         // 使用DMA中断采集数据，不会容易丢失数据
{
    if(DMA_GetITStatus(DMA2_Stream1,DMA_IT_TCIF1)!=RESET)
    {
        
        flag_ADC2=1;
        TIM_Cmd(TIM3, DISABLE);
        DMA_ClearITPendingBit(DMA2_Stream1,DMA_IT_TCIF1);          
        //printf("3\r\n"); 
    }
}
