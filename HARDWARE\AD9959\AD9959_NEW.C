/*---------------------------------------------------------
modify author�� XHMM
file: AD9959.c
version�� v0.0
Date��2025.7.25
state�� paseed
----------------------------------------------------------*/

#include "AD9959_NEW.H"

AD9959MSG AD9959msg;
uint8_t CSR_DATA0[1] = {0x10};      //�� CH0
uint8_t CSR_DATA1[1] = {0x20};      // �� CH1
uint8_t CSR_DATA2[1] = {0x40};      // �� CH2
uint8_t CSR_DATA3[1] = {0x80};      // �� CH3		
																	
uint8_t FR1_DATA[3] = {0xD0,0x00,0x00};//20��Ƶ Charge pump control = 75uA FR1<23> -- VCO gain control =0ʱ system clock below 160 MHz; 
//uint8_t FR1_DATA[3] = {0xD0,0x54,0x00};

//uint8_t FR2_DATA[2] = {0x00,0x00};//default Value = 0x0000
uint8_t FR2_DATA[2] ={0x20,0x00};

uint8_t CFR_DATA[3] = {0x00,0x03,0x02};//default Value = 0x000302	 
//uint8_t CFR_DATA[3] =  {0x80,0xC3,0x00};

																	
uint8_t CPOW0_DATA[2] = {0x00,0x00};//default Value = 0x0000   @ = POW/2^14*360
																	


uint8_t LSRR_DATA[2] = {0x00,0x00};  //default Value = 0x----
//uint8_t LSRR_DATA[4] = {0x00,0x7D,0x00,0x7D};

																	
uint8_t RDW_DATA[4] = {0x00,0x00,0x00,0x00};//default Value = 0x--------
//uint8_t RDW_DATA[4] = {0x0,0x00,0x00,0x67};
																	
uint8_t FDW_DATA[4] = {0x00,0x00,0x00,0x00};//default Value = 0x--------
//uint8_t FDW_DATA[4] = {0x00,0x00,0x00,0x67};


//FR1_DATA[3] = {0xD0,0x54,0x00}

//FR2_DATA[2] = {0x20,0x00};

//CFR_DATA[3] = {0x80,0xC3,0x00}

//CFTW0_DATA0[4] = {0x00,0x83,0x12,0x6F};

//LSRR_DATA[4] = {0x00,0x7D,0x00,0x7D}

//RDW_DATA[4] = {0x0,0x00,0x00,0x67};

//FDW_DATA[4] = {0x00,0x00,0x00,0x67};

//CW_DATA[4] = {0x33,0x33,0x33,0x33};

//AD9959��ʼ��
void Init_AD9959(void)
{
    AD9959MSGInit();
    GPIO_InitTypeDef GPIO_InitStruct;

    // 使能GPIO时钟 (只保留必要的GPIO)
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB, ENABLE);
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOC, ENABLE);
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOD, ENABLE);

    // 配置GPIO参数
    GPIO_InitStruct.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStruct.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStruct.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_InitStruct.GPIO_Speed = GPIO_Speed_100MHz;

    // 初始化控制引脚
    GPIO_InitStruct.GPIO_Pin = AD9959_SDIO_Pin;
    GPIO_Init(AD9959_SDIO_GPIO, &GPIO_InitStruct);

    GPIO_InitStruct.GPIO_Pin = AD9959_SCLK_Pin;
    GPIO_Init(AD9959_SCLK_GPIO, &GPIO_InitStruct);

    GPIO_InitStruct.GPIO_Pin = AD9959_CS_Pin;
    GPIO_Init(AD9959_CS_GPIO, &GPIO_InitStruct);

    GPIO_InitStruct.GPIO_Pin = AD9959_UPDATE_Pin;
    GPIO_Init(AD9959_UPDATE_GPIO, &GPIO_InitStruct);

    GPIO_InitStruct.GPIO_Pin = AD9959_RESET_Pin;
    GPIO_Init(AD9959_RESET_GPIO, &GPIO_InitStruct);

    Intserve();  //IO�ڳ�ʼ��
    IntReset();  //AD9959��λ  
    
    //WriteData_AD9959(CFR_ADDR,3,CFR_DATA,1);
    /////////////////////////////////////WriteData_AD9959(FR1_ADDR,3,FR1_DATA,1);//д���ܼĴ���1
  //WriteData_AD9959(FR2_ADDR,2,FR2_DATA,0);
    //WriteData_AD9959(CFR_ADDR,3,CFR_DATA,1);
//    WriteData_AD9959(CPOW0_ADDR,2,CPOW0_DATA,0);
//  WriteData_AD9959(ACR_ADDR,3,ACR_DATA,0);
  //WriteData_AD9959(LSRR_ADDR,2,LSRR_DATA,0);
  //WriteData_AD9959(RDW_ADDR,2,RDW_DATA,0);
  //WriteData_AD9959(FDW_ADDR,4,FDW_DATA,1);
//д���ʼƵ��
    
//    WriteAmplitude(3, AD9959msg.CurrentAmp[3],0);
//    WriteAmplitude(0, AD9959msg.CurrentAmp[3],0);
//    WriteAmplitude(1, AD9959msg.CurrentAmp[3],0);
//    WriteAmplitude(2, AD9959msg.CurrentAmp[3],0);
//    
//    WriteFreq(3,AD9959msg.CurrentFreq[3],0);
//    WriteFreq(0,AD9959msg.CurrentFreq[0],0); 
//    WriteFreq(1,AD9959msg.CurrentFreq[1],0);
//    WriteFreq(2,AD9959msg.CurrentFreq[2],0);
    
    //IO_Update();
    //IO_Update();  
		
    WriteData_AD9959(0x01U, 3, FR1_DATA, 1);//��????????1
    WriteData_AD9959(0x02U, 2, FR2_DATA, 1);											 
													 
    Intserve();  //IO�ڳ�ʼ��
    IntReset();  //AD9959��λ 

    WriteData_AD9959(0x01U, 3, FR1_DATA, 1);//??????1
    WriteData_AD9959(0x02U, 2, FR2_DATA, 1);
		
		WriteAmplitude(3, AD9959msg.CurrentAmp[3],0);
    WriteAmplitude(0, AD9959msg.CurrentAmp[3],0);
    WriteAmplitude(1, AD9959msg.CurrentAmp[3],0);
    WriteAmplitude(2, AD9959msg.CurrentAmp[3],0);
    
    WriteFreq(3,AD9959msg.CurrentFreq[3],0);
    WriteFreq(0,AD9959msg.CurrentFreq[0],0); 
    WriteFreq(1,AD9959msg.CurrentFreq[1],0);
    WriteFreq(2,AD9959msg.CurrentFreq[2],0);
		
} 

void AD9959MSGInit(void)
{
    AD9959msg.CurrentFreq[0]=30*MHz;
    AD9959msg.CurrentFreq[1]=30*MHz;
    AD9959msg.CurrentFreq[2]=30*MHz;
    AD9959msg.CurrentFreq[3]=30*MHz;
    AD9959msg.CurrentPhase[0]=0;
    AD9959msg.CurrentPhase[1]=0;
    AD9959msg.CurrentPhase[2]=0;
    AD9959msg.CurrentPhase[3]=0;
    AD9959msg.CurrentAmp[0]=1023;
    AD9959msg.CurrentAmp[1]=1023;
    AD9959msg.CurrentAmp[2]=1023;
    AD9959msg.CurrentAmp[3]=1023;
}
//��ʱ
void delay1 (uint32_t length)
{
    length = length*12;
    while(length--);
}
//IO�ڳ�ʼ��
void Intserve(void)
{
    // 初始化控制引脚状态
    GPIO_SetBits(AD9959_CS_GPIO, AD9959_CS_Pin);   // CS高电平
    GPIO_ResetBits(AD9959_SCLK_GPIO, AD9959_SCLK_Pin); // SCLK低电平
    GPIO_ResetBits(AD9959_UPDATE_GPIO, AD9959_UPDATE_Pin); // UPDATE低电平
    GPIO_ResetBits(AD9959_RESET_GPIO, AD9959_RESET_Pin); // RESET低电平
    GPIO_ResetBits(AD9959_SDIO_GPIO, AD9959_SDIO_Pin); // SDIO低电平
}
//AD9959��λ
void IntReset(void)
{
    //AD9959_Reset = 0;
    GPIO_ResetBits(AD9959_RESET_GPIO, AD9959_RESET_Pin);
    delay1(1);
    //AD9959_Reset = 1;
    GPIO_SetBits(AD9959_RESET_GPIO, AD9959_RESET_Pin);
    delay1(30);
    //AD9959_Reset = 0;
    GPIO_ResetBits(AD9959_RESET_GPIO, AD9959_RESET_Pin);
}
 //AD9959��������
void IO_Update(void)
{
    //AD9959_UPDATE = 0;
    GPIO_ResetBits(AD9959_UPDATE_GPIO, AD9959_UPDATE_Pin);
    delay1(2);
    //AD9959_UPDATE = 1;
    GPIO_SetBits(AD9959_UPDATE_GPIO, AD9959_UPDATE_Pin);
    delay1(4);
    //AD9959_UPDATE = 0;
    GPIO_ResetBits(AD9959_UPDATE_GPIO, AD9959_UPDATE_Pin);
}
/*--------------------------------------------
�������ܣ�������ͨ��SPI��AD9959д����
RegisterAddress: �Ĵ�����ַ
NumberofRegisters: �����ֽ���
*RegisterData: ������ʼ��ַ
temp: �Ƿ����IO�Ĵ���
----------------------------------------------*/
void WriteData_AD9959(uint8_t RegisterAddress, uint8_t NumberofRegisters, uint8_t *RegisterData,uint8_t temp)
{
    uint8_t	ControlValue = 0;
    uint8_t	ValueToWrite = 0;
    uint8_t	RegisterIndex = 0;
    uint8_t	i = 0;

    ControlValue = RegisterAddress;
    //д���ַ
    AD9959_SCLK_0;
    AD9959_CS_0;	 
    for(i=0; i<8; i++)
    {
        AD9959_SCLK_0;
        if(0x80 == (ControlValue & 0x80))
				{AD9959_SDIO_1;	  }
        else
				{ AD9959_SDIO_0;	  }
        AD9959_SCLK_1;
        ControlValue <<= 1;
    }
    AD9959_SCLK_0;
    //д������
    for (RegisterIndex=0; RegisterIndex<NumberofRegisters; RegisterIndex++)
    {
        ValueToWrite = RegisterData[RegisterIndex];
        for (i=0; i<8; i++)
        {
            AD9959_SCLK_0;
            if(0x80 == (ValueToWrite & 0x80))
						{AD9959_SDIO_1;}	  
            else
						{AD9959_SDIO_0;	}  
            AD9959_SCLK_1;
            ValueToWrite <<= 1;
        }
        AD9959_SCLK_0;		
    }	
    if(temp==1)
        IO_Update();	
  AD9959_CS_1;
} 
//void WriteData_AD9959(uint8_t RegisterAddress, uint8_t NumberofRegisters, uint8_t *RegisterData,uint8_t temp)
//{

//HAL_SPI_Transmit_DMA(&hspi1,txbuff,rxbuff,8);

//}
/*---------------------------------------
�������ܣ�����ͨ�����Ƶ��
Channel:  ���ͨ��
Freq:     ���Ƶ��
---------------------------------------*/
void Write_frequence(uint8_t Channel,uint32_t Freq)
{	 
    uint8_t CFTW0_DATA[4] ={0x00,0x00,0x00,0x00};	//�м����
    uint32_t Temp;            
    Temp=(uint32_t)Freq*8.589934592;	   //������Ƶ�����ӷ�Ϊ�ĸ��ֽ�  4.294967296=(2^32)/500000000
    CFTW0_DATA[3]=(uint8_t)Temp;
    CFTW0_DATA[2]=(uint8_t)(Temp>>8);
    CFTW0_DATA[1]=(uint8_t)(Temp>>16);
    CFTW0_DATA[0]=(uint8_t)(Temp>>24);
    if(Channel==0)	  
    {
        AD9959msg.CurrentFreq[0]=Freq;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA0,1);//���ƼĴ���д��CH0ͨ��
        WriteData_AD9959(CFTW0_ADDR,4,CFTW0_DATA,1);//CTW0 address 0x04.���CH0�趨Ƶ��
    }
    else if(Channel==1)	
    {
        AD9959msg.CurrentFreq[1]=Freq;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA1,1);//���ƼĴ���д��CH1ͨ��
        WriteData_AD9959(CFTW0_ADDR,4,CFTW0_DATA,1);//CTW0 address 0x04.���CH1�趨Ƶ��	
    }
    else if(Channel==2)	
    {
        AD9959msg.CurrentFreq[2]=Freq;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA2,1);    //���ƼĴ���д��CH2ͨ��
        WriteData_AD9959(CFTW0_ADDR,4,CFTW0_DATA,1);//CTW0 address 0x04.���CH2�趨Ƶ��	
    }
    else if(Channel==3)	
    {
        AD9959msg.CurrentFreq[3]=Freq;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA3,1);    //���ƼĴ���д��CH3ͨ��
        WriteData_AD9959(CFTW0_ADDR,4,CFTW0_DATA,1);//CTW0 address 0x04.���CH3�趨Ƶ��	
    }																																																																										 

} 


/*---------------------------------------
�������ܣ�����ͨ�����Ƶ��
Channel:  ���ͨ��
Freq:     ���Ƶ��
mode:   0: ���������¼Ĵ����� 1: ��������
---------------------------------------*/

void WriteFreq(uint8_t Channel,uint32_t Freq,uint8_t mode)
{
    uint8_t CFTW0_DATA[4] ={0x00,0x00,0x00,0x00};	//�м����
    uint32_t Temp;            
    Temp=(uint32_t)Freq*8.589934592;	   //������Ƶ�����ӷ�Ϊ�ĸ��ֽ�  8.589934592=(2^32)/500000000
    CFTW0_DATA[3]=(uint8_t)Temp;
    CFTW0_DATA[2]=(uint8_t)(Temp>>8);
    CFTW0_DATA[1]=(uint8_t)(Temp>>16);
    CFTW0_DATA[0]=(uint8_t)(Temp>>24);
    if(Channel==0)	  
    {
        AD9959msg.CurrentFreq[0]=Freq;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA0,mode);//���ƼĴ���д��CH0ͨ��
        WriteData_AD9959(CFTW0_ADDR,4,CFTW0_DATA,mode);//CTW0 address 0x04.���CH0�趨Ƶ��
    }
    else if(Channel==1)	
    {
        AD9959msg.CurrentFreq[1]=Freq;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA1,mode);//���ƼĴ���д��CH1ͨ��
        WriteData_AD9959(CFTW0_ADDR,4,CFTW0_DATA,mode);//CTW0 address 0x04.���CH1�趨Ƶ��	
    }
    else if(Channel==2)	
    {
        AD9959msg.CurrentFreq[2]=Freq;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA2,mode);    //���ƼĴ���д��CH2ͨ��
        WriteData_AD9959(CFTW0_ADDR,4,CFTW0_DATA,mode);//CTW0 address 0x04.���CH2�趨Ƶ��	
    }
    else if(Channel==3)	
    {
        AD9959msg.CurrentFreq[3]=Freq;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA3,mode);    //���ƼĴ���д��CH3ͨ��
        WriteData_AD9959(CFTW0_ADDR,4,CFTW0_DATA,mode);//CTW0 address 0x04.���CH3�趨Ƶ��	
    }	
}

void WriteFreqOrtho(uint32_t Freq)
{
    uint8_t CFTW0_DATA[4] ={0x00,0x00,0x00,0x00};	//�м����
    uint32_t Temp;
    uint16_t A_temp;//=0x23ff;
    uint8_t ACR_DATA[3] = {0x00,0x00,0x00};//default Value = 0x--0000 Rest = 18.91/Iout 
    uint8_t csr[1]={0xF0};
    uint64_t  temp=Freq;
    
    if(Freq>160*MHz)
        Freq=160*MHz;
    
    AD9959msg.CurrentFreq[0]=AD9959msg.CurrentFreq[1]=AD9959msg.CurrentFreq[2]=AD9959msg.CurrentFreq[3]=Freq;
    
    A_temp=(680+(temp*258)/(160*MHz))|0x1000;       //765
    if(temp>=145*MHz)
        A_temp=A_temp+(temp*35)/(160*MHz);
    if(temp>=152*MHz)
        A_temp=A_temp+(temp*20)/(160*MHz);
    if(temp>=155*MHz)
        A_temp=A_temp+(temp*30)/(160*MHz);
    ACR_DATA[2] = (uint8_t)A_temp;       //��λ����
    ACR_DATA[1] = (uint8_t)(A_temp>>8); //��λ����
    Temp=(uint32_t)Freq*8.589934592;	   //������Ƶ�����ӷ�Ϊ�ĸ��ֽ�  8.589934592=(2^32)/500000000
    CFTW0_DATA[3]=(uint8_t)Temp;
    CFTW0_DATA[2]=(uint8_t)(Temp>>8);
    CFTW0_DATA[1]=(uint8_t)(Temp>>16);
    CFTW0_DATA[0]=(uint8_t)(Temp>>24);
    
    if(AD9959msg.CurrentPhase[0]!=0||AD9959msg.CurrentPhase[1]!=0||AD9959msg.CurrentPhase[2]!=4096*2||AD9959msg.CurrentPhase[3]!=4096*3)
    {
        WritePhase(0,0,0);
        WritePhase(1,4096,0);
        WritePhase(2,4096*2,0);
        WritePhase(3,4096*3,0);
        AD9959msg.CurrentPhase[0]=0;
        AD9959msg.CurrentPhase[1]=4096;
        AD9959msg.CurrentPhase[2]=4096*2;
        AD9959msg.CurrentPhase[3]=4096*3;
    }
    
    WriteData_AD9959(CSR_ADDR,1,csr,0);
    
    WriteData_AD9959(ACR_ADDR,3,ACR_DATA,0);
    
    WriteData_AD9959(CFTW0_ADDR,4,CFTW0_DATA,0);
    
    IO_Update();
}
/*---------------------------------------
�������ܣ�����ͨ���������
Channel:  ���ͨ��
Ampli:    �������
---------------------------------------*/
void Write_Amplitude(uint8_t Channel, uint16_t Ampli)
{ 
    uint16_t A_temp;//=0x23ff;
    uint8_t ACR_DATA[3] = {0x00,0x00,0x00};//default Value = 0x--0000 Rest = 18.91/Iout 

    A_temp=Ampli|0x1000;
    ACR_DATA[2] = (uint8_t)A_temp;       //��λ����
    ACR_DATA[1] = (uint8_t)(A_temp>>8); //��λ����
    if(Channel==0)
    {
        AD9959msg.CurrentAmp[0]=Ampli;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA0,1); 
        WriteData_AD9959(ACR_ADDR,3,ACR_DATA,1); 
    }
    else if(Channel==1)
    {
        AD9959msg.CurrentAmp[1]=Ampli;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA1,1); 
        WriteData_AD9959(ACR_ADDR,3,ACR_DATA,1);
    }
    else if(Channel==2)
    {
        AD9959msg.CurrentAmp[2]=Ampli;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA2,1); 
        WriteData_AD9959(ACR_ADDR,3,ACR_DATA,1); 
    }
    else if(Channel==3)
    {
        AD9959msg.CurrentAmp[3]=Ampli;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA3,1); 
        WriteData_AD9959(ACR_ADDR,3,ACR_DATA,1); 
    }
}
/*---------------------------------------
�������ܣ�����ͨ���������
��Ӧ��ϵ�� 25mv/100 �������
Channel:  ���ͨ��
Ampli:    �������
mode :   0:       1:
---------------------------------------*/
void WriteAmplitude(uint8_t Channel, uint16_t Ampli, uint8_t mode)
{
    uint16_t A_temp;//=0x23ff;
    uint8_t ACR_DATA[3] = {0x00,0x00,0x00};//default Value = 0x--0000 Rest = 18.91/Iout 

    A_temp=Ampli|0x1000;
    ACR_DATA[2] = (uint8_t)A_temp;       //��λ����
    ACR_DATA[1] = (uint8_t)(A_temp>>8); //��λ����
    if(Channel==0)
    {
        AD9959msg.CurrentAmp[0]=Ampli;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA0,mode); 
        WriteData_AD9959(ACR_ADDR,3,ACR_DATA,mode); 
    }
    else if(Channel==1)
    {
        AD9959msg.CurrentAmp[1]=Ampli;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA1,mode); 
        WriteData_AD9959(ACR_ADDR,3,ACR_DATA,mode);
    }
    else if(Channel==2)
    {
        AD9959msg.CurrentAmp[2]=Ampli;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA2,mode); 
        WriteData_AD9959(ACR_ADDR,3,ACR_DATA,mode); 
    }
    else if(Channel==3)
    {
        AD9959msg.CurrentAmp[3]=Ampli;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA3,mode); 
        WriteData_AD9959(ACR_ADDR,3,ACR_DATA,mode); 
    }    
}



uint16_t Phase_2_AD9959(float32_t Phase)
{
	Phase=fmodf(Phase,360.0f);
	if(Phase<0)
	{
		Phase=360.0f+Phase;
	}
	return (uint16_t)(Phase*16383.0f/360.0f);
}

/*---------------------------------------
�������ܣ�����ͨ�������λ
Channel:  ���ͨ��
Phase:    �����λ,��Χ��0~16383(��Ӧ�Ƕȣ�0��~360��)
---------------------------------------*/
void Write_Phase(uint8_t Channel,uint16_t Phase)
{
    uint16_t P_temp=0;
    P_temp=(uint16_t)Phase;
    CPOW0_DATA[1]=(uint8_t)P_temp;
    CPOW0_DATA[0]=(uint8_t)(P_temp>>8);
    if(Channel==0)
    {
        AD9959msg.CurrentPhase[3]=Phase;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA0,0); 
        WriteData_AD9959(CPOW0_ADDR,2,CPOW0_DATA,0);
    }
    else if(Channel==1)
    {
        AD9959msg.CurrentPhase[3]=Phase;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA1,0); 
        WriteData_AD9959(CPOW0_ADDR,2,CPOW0_DATA,0);
    }
    else if(Channel==2)
    {
        AD9959msg.CurrentPhase[3]=Phase;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA2,0); 
        WriteData_AD9959(CPOW0_ADDR,2,CPOW0_DATA,0);
    }
    else if(Channel==3)
    {
        AD9959msg.CurrentPhase[3]=Phase;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA3,0); 
        WriteData_AD9959(CPOW0_ADDR,2,CPOW0_DATA,0);
    }
}	 

/*---------------------------------------
�������ܣ�����ͨ�������λ
Channel:  ���ͨ��
Phase:    �����λ,��Χ��0~16383(��Ӧ�Ƕȣ�0��~360��)
mode :    0:���������£� 1:��������
---------------------------------------*/
void WritePhase(uint8_t Channel,uint16_t Phase, uint8_t mode)
{
    uint16_t P_temp=0;
    P_temp=(uint16_t)Phase;
    CPOW0_DATA[1]=(uint8_t)P_temp;
    CPOW0_DATA[0]=(uint8_t)(P_temp>>8);
    if(Channel==0)
    {
        AD9959msg.CurrentPhase[0]=Phase;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA0,mode); 
        WriteData_AD9959(CPOW0_ADDR,2,CPOW0_DATA,mode);
    }
    else if(Channel==1)
    {
        AD9959msg.CurrentPhase[1]=Phase;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA1,mode); 
        WriteData_AD9959(CPOW0_ADDR,2,CPOW0_DATA,mode);
    }
    else if(Channel==2)
    {
        AD9959msg.CurrentPhase[2]=Phase;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA2,mode); 
        WriteData_AD9959(CPOW0_ADDR,2,CPOW0_DATA,mode);
    }
    else if(Channel==3)
    {
        AD9959msg.CurrentPhase[3]=Phase;
        WriteData_AD9959(CSR_ADDR,1,CSR_DATA3,mode); 
        WriteData_AD9959(CPOW0_ADDR,2,CPOW0_DATA,mode);
    }
}

/////////////////////////////////////////////////
//function AD9959FreqSweep
//test not pass
/////////////////////////////////////////////////
//void AD9959FreqSweep(uint32_t SFreq,uint32_t EFreq,uint32_t Step,double StayTime,uint8_t mode)
//{
//    uint8_t tempCSR[1]={0xF0};
//    //uint8_t tempCFR[3]={0x80,0xE3,0x02};
//	uint8_t tempCFR[3]={0x89,0xF3,0x03};
////    uint8_t tempFR1[3]={0xD0,0x00,0x00};
//    uint8_t tempCFTWS[4]={0x00,0x00,0x00,0x00};
//    uint8_t tempCFTWE[4]={0x00,0x00,0x00,0x00};
//    uint8_t tempRDW[4]={0x00,0x00,0x00,0x00};
//    uint8_t tempFDW[4]={0x00,0x00,0x00,0x00};
//    uint8_t tempLSRR[2]={0xFF,0xFF}; //LSRR[15:8] is Falling sweep ramp rate,LSRR[7:0] is Rising sweep ramp rate
//    CalculateFreq(SFreq,tempCFTWS);
//    CalculateFreq(EFreq,tempCFTWE);
//    CalculateFreq(Step,tempRDW);
//    CalculateFreq(Step,tempFDW);
//    CalculateStayTime(StayTime,tempLSRR);
//    WritePhase(0,0,0);
//    WritePhase(1,4096,0);
//    WritePhase(2,4096*2,0);
//    WritePhase(3,4096*3,0);
//    IO_Update();
//    if(mode==LINEAR)
//    {
//        WriteData_AD9959(CSR_ADDR,1,tempCSR,0);
//        WriteData_AD9959(CFR_ADDR,3,tempCFR,0);
//        WriteData_AD9959(FREQS0_ADDR,4,tempCFTWS,0);
//        WriteData_AD9959(FREQE0_ADDR,4,tempCFTWE,0);
//        WriteData_AD9959(RDW_ADDR,4,tempRDW,0);
//        WriteData_AD9959(FDW_ADDR,4,tempFDW,0);
//        WriteData_AD9959(LSRR_ADDR,2,tempLSRR,0);
//        
//        IO_Update();
//		 HAL_GPIO_WritePin(GPIOB, AD9959_PS0_Pin, GPIO_PIN_SET);
//		 delay1(10);   // ��ʱ��8ns��25MHzʱ���£�10�������㹻��
//        HAL_GPIO_WritePin(GPIOB, AD9959_PS0_Pin, GPIO_PIN_RESET);
//      // AD9959_PS0=1;
//    }
//    else if(mode==FSK)
//    {
//        
//    }
//}

//void AD9959FreqSweep(uint32_t SFreq, uint32_t EFreq, uint32_t Step, double StayTime, uint8_t mode, uint8_t channel)
//{
//    // ѡ��ͨ�����ο�����������
//    uint8_t tempCSR[1] = {0xF0};
//    uint8_t tempCFR[3] = {0x89, 0xF3, 0x03};
////	 uint8_t tempCFR[3] = {0x00, 0x00, 0x00};
//    uint8_t tempFR1[3] = {0xD3, 0x00, 0x00};  // ����FR1�Ĵ�������
//    uint8_t tempCFTWS[4] = {0x00, 0x00, 0x00, 0x00};
//    uint8_t tempCFTWE[4] = {0x00, 0x00, 0x00, 0x00};
//    uint8_t tempRDW[4] = {0x00, 0x00, 0x00, 0x00};
//    uint8_t tempFDW[4] = {0x00, 0x00, 0x00, 0x00};
//    uint8_t tempLSRR[2] = {0xFF, 0xFF};
//    
//    // ����Ƶ����
//    CalculateFreq(SFreq, tempCFTWS);
//    CalculateFreq(EFreq, tempCFTWE);
//    CalculateFreq(Step, tempRDW);
//    CalculateFreq(Step, tempFDW);
//    CalculateStayTime(StayTime, tempLSRR);
//    
//    // ���ø�ͨ����λ
//    WritePhase(0, 0, 0);
//    WritePhase(1, 4096, 0);
//    WritePhase(2, 4096*2, 0);
//    WritePhase(3, 4096*3, 0);
//    
//    IO_Update();
//    
//    if(mode == LINEAR)
//    {
//        // ����CSR
//        WriteData_AD9959(CSR_ADDR, 1, tempCSR, 0);
//        
//        // ����CFR
//        WriteData_AD9959(CFR_ADDR, 3, tempCFR, 0);
//        
//        // ����FR1�Ĵ�������
//        WriteData_AD9959(FR1_ADDR, 3, tempFR1, 0);
//        
//        // ����Ƶ�ʲ���
//        WriteData_AD9959(FREQS0_ADDR, 4, tempCFTWS, 0);
//        WriteData_AD9959(FREQE0_ADDR, 4, tempCFTWE, 0);
//        WriteData_AD9959(RDW_ADDR, 4, tempRDW, 0);
//        WriteData_AD9959(FDW_ADDR, 4, tempFDW, 0);
//        WriteData_AD9959(LSRR_ADDR, 2, tempLSRR, 0);
//        
//        IO_Update();
//        
//        // ѡ��ָ��ͨ��������channel������
//        HAL_GPIO_WritePin(GPIOB, AD9959_PS0_Pin, (channel != 0) ? GPIO_PIN_SET : GPIO_PIN_RESET);
//        HAL_GPIO_WritePin(GPIOB, AD9959_PS1_Pin, (channel != 1) ? GPIO_PIN_SET : GPIO_PIN_RESET);
//        HAL_GPIO_WritePin(GPIOB, AD9959_PS2_Pin, (channel != 2) ? GPIO_PIN_SET : GPIO_PIN_RESET);
//        HAL_GPIO_WritePin(GPIOB, AD9959_PS3_Pin, (channel != 3) ? GPIO_PIN_SET : GPIO_PIN_RESET);
//        delay1(10);
//    }
//    else if(mode == FSK)
//    {
//        // ʵ��FSKģʽ
//        // �ο����������е�FSK����
//        tempCSR[0] |= (0x01 << channel);  // ʹ��ָ��ͨ��
//        WriteData_AD9959(CSR_ADDR, 1, tempCSR, 0);
//        
//        // ��������Ƶ��������FSK
//        WriteData_AD9959(FREQS0_ADDR, 4, tempCFTWS, 0);  // FSKƵ��1
//        WriteData_AD9959(FREQE0_ADDR, 4, tempCFTWE, 0);  // FSKƵ��2
//        
//        // ����CFR������FSKģʽ
//        tempCFR[0] &= ~0x04;  // �������ɨƵλ
//        tempCFR[0] |= 0x08;   // ����FSKģʽ
//        WriteData_AD9959(CFR_ADDR, 3, tempCFR, 0);
//        
//        // ���õ��ƿ��ƼĴ�����ʹ��PS0���ſ���FSK
//        uint8_t tempMCR[1] = {0x01};  // ����MCR[0]Ϊ1��ʹ��PS0���ſ���FSK
//        WriteData_AD9959(MCR_ADDR, 1, tempMCR, 0);
//        
//        IO_Update();
//    }
//}

//�����ã�727

void AD9959FreqSweep(uint32_t SFreq, uint32_t EFreq, uint32_t Step, double StayTime, uint8_t mode, uint8_t channel)
{
    // ѡ��ͨ�����ο�����������
    uint8_t tempCSR[1] = {0xF0};
    uint8_t tempCFR[3] = {0x00, 0x00, 0x00};
    uint8_t tempFR1[3] = {0xD3, 0x00, 0x00};  // ����FR1�Ĵ�������
    uint8_t tempCFTWS[4] = {0x00, 0x00, 0x00, 0x00};
    uint8_t tempCFRRead[3] = {0x00, 0x00, 0x00}; // ���ڶ�ȡ��ǰCFR����
    uint8_t tempCFTWE[4] = {0x00, 0x00, 0x00, 0x00};
    uint8_t tempRDW[4] = {0x00, 0x00, 0x00, 0x00};
    uint8_t tempFDW[4] = {0x00, 0x00, 0x00, 0x00};
    uint8_t tempLSRR[2] = {0xFF, 0xFF};
    
    // ����Ƶ����
    CalculateFreq(SFreq, tempCFTWS);
    CalculateFreq(EFreq, tempCFTWE);
    CalculateFreq(Step, tempRDW);
    CalculateFreq(Step, tempFDW);
    CalculateStayTime(StayTime, tempLSRR);
    
    // ���ø�ͨ����λ
    WritePhase(0, 0, 0);
    WritePhase(1, 4096, 0);
    WritePhase(2, 4096*2, 0);
    WritePhase(3, 4096*3, 0);
    
    IO_Update();
    
    if(mode == LINEAR)
    {
        // ��ȡ��ǰCFR���ã��Ա㱣���������������õ�λ
        ReadData_AD9959(CFR_ADDR, 3, tempCFRRead);
        
        // ����CFR - ��������ɨƵģʽ
        tempCFR[0] = tempCFRRead[0] | 0x80 | 0x04; // ʹ������ͨ������������ɨƵ
        tempCFR[1] = tempCFRRead[1] | 0x40;        // ����Ƶ�ʿ���
        tempCFR[2] = tempCFRRead[2];               // ����ԭ��ֵ
        
        // ����CSR
        WriteData_AD9959(CSR_ADDR, 1, tempCSR, 0);
        
        // ����CFR
        WriteData_AD9959(CFR_ADDR, 3, tempCFR, 0);
        
        // ����FR1�Ĵ�������
        WriteData_AD9959(FR1_ADDR, 3, tempFR1, 0);
        
        // ����Ƶ�ʲ���
        WriteData_AD9959(FREQS0_ADDR, 4, tempCFTWS, 0);
        WriteData_AD9959(FREQE0_ADDR, 4, tempCFTWE, 0);
        WriteData_AD9959(RDW_ADDR, 4, tempRDW, 0);
        WriteData_AD9959(FDW_ADDR, 4, tempFDW, 0);
        WriteData_AD9959(LSRR_ADDR, 2, tempLSRR, 0);
        
        IO_Update();
        
        // ѡ��ָ��ͨ��������channel������
        GPIO_WriteBit(AD9959_PS0_GPIO, AD9959_PS0_Pin, (channel != 0) ? Bit_SET : Bit_RESET);
        GPIO_WriteBit(AD9959_PS1_GPIO, AD9959_PS1_Pin, (channel != 1) ? Bit_SET : Bit_RESET);
        GPIO_WriteBit(AD9959_PS2_GPIO, AD9959_PS2_Pin, (channel != 2) ? Bit_SET : Bit_RESET);
        GPIO_WriteBit(AD9959_PS3_GPIO, AD9959_PS3_Pin, (channel != 3) ? Bit_SET : Bit_RESET);
        delay1(10);
        
        // ����ɨƵ - ͨ��PSx����
        // �Ƚ���ѡͨ����PSx�����øߣ����õ��Դ���ɨƵ
        switch(channel) {
            case 0:
                GPIO_SetBits(AD9959_PS0_GPIO, AD9959_PS0_Pin);
                delay1(10);
                GPIO_ResetBits(AD9959_PS0_GPIO, AD9959_PS0_Pin);
                break;
            case 1:
                GPIO_SetBits(AD9959_PS1_GPIO, AD9959_PS1_Pin);
                delay1(10);
                GPIO_ResetBits(AD9959_PS1_GPIO, AD9959_PS1_Pin);
                break;
            case 2:
                GPIO_SetBits(AD9959_PS2_GPIO, AD9959_PS2_Pin);
                delay1(10);
                GPIO_ResetBits(AD9959_PS2_GPIO, AD9959_PS2_Pin);
                break;
            case 3:
                GPIO_SetBits(AD9959_PS3_GPIO, AD9959_PS3_Pin);
                delay1(10);
                GPIO_ResetBits(AD9959_PS3_GPIO, AD9959_PS3_Pin);
                break;
        }
    }
    else if(mode == FSK)
    {
        // ʵ��FSKģʽ
        // �ο����������е�FSK����
        tempCSR[0] |= (0x01 << channel);  // ʹ��ָ��ͨ��
        WriteData_AD9959(CSR_ADDR, 1, tempCSR, 0);
        
        // ��������Ƶ��������FSK
        WriteData_AD9959(FREQS0_ADDR, 4, tempCFTWS, 0);  // FSKƵ��1
        WriteData_AD9959(FREQE0_ADDR, 4, tempCFTWE, 0);  // FSKƵ��2
        
        // ����CFR������FSKģʽ
        tempCFR[0] &= ~0x04;  // �������ɨƵλ
        tempCFR[0] |= 0x08;   // ����FSKģʽ
        WriteData_AD9959(CFR_ADDR, 3, tempCFR, 0);
        
        // ���õ��ƿ��ƼĴ�����ʹ��PS0���ſ���FSK
        uint8_t tempMCR[1] = {0x01};  // ����MCR[0]Ϊ1��ʹ��PS0���ſ���FSK
        WriteData_AD9959(MCR_ADDR, 1, tempMCR, 0);
        
        IO_Update();
    }
}
/*---------------------------------------
�������ܣ�AD9959����ɨƵ����
Channel:  ���ͨ�� (0-3)
StartFreq: ��ʼƵ�� (Hz)
StopFreq:  ��ֹƵ�� (Hz)
Step:     Ƶ�ʲ��� (Hz)
Time:     ÿ��ͣ��ʱ�� (ns)
NO_DWELL: �Ƿ����ͣ��ʱ�� (0:������, 1:����)
������
---------------------------------------*/
void SweepFrequency(uint8_t Channel, uint32_t StartFreq, uint32_t StopFreq, uint32_t Step, uint32_t Time, uint8_t NO_DWELL)
{
    // ȷ��ͨ�������Ϸ�
    if (Channel > 3) return;
    
    // ѡ��ָ��ͨ��
    Channel_Select(Channel);
    
    // ��ȡ��ǰCFR�Ĵ���ֵ
    uint8_t currentCFR[3] = {0};
    ReadData_AD9959(CFR_ADDR, 3, currentCFR);
    
    // ����CFR�Ĵ�������������ɨƵģʽ
    uint8_t tempCFR[3] = {
        currentCFR[0] | 0x80 | 0x04,  // ʹ��ͨ������������ɨƵģʽ
        currentCFR[1] | 0x40,         // ����Ƶ�ʿ���
        currentCFR[2]                // ����ԭ��ֵ
    };
    
    // ����no-dwellλ (CFR[13])
    if (NO_DWELL)
        tempCFR[1] |= 0x80;
    else
        tempCFR[1] &= ~0x80;
    
    // ����FR1�Ĵ���
    uint8_t tempFR1[3] = {0xD3, 0x00, 0x00};
    
    // ����Ƶ����
    uint8_t tempCFTWS[4] = {0};  // ��ʼƵ����
    uint8_t tempCFTWE[4] = {0};  // ��ֹƵ����
    uint8_t tempRDW[4] = {0};    // ����������
    uint8_t tempFDW[4] = {0};    // �½�������
    
    CalculateFreq(StartFreq, tempCFTWS);
    CalculateFreq(StopFreq, tempCFTWE);
    CalculateFreq(Step, tempRDW);
    CalculateFreq(Step, tempFDW);
    
    // ����ͣ��ʱ��
    uint8_t tempLSRR[2] = {0};
    CalculateStayTime(Time, tempLSRR);
    
    // д����������
    WriteData_AD9959(CFR_ADDR, 3, tempCFR, 0);     // ���ƹ��ܼĴ���
    WriteData_AD9959(FR1_ADDR, 3, tempFR1, 0);     // ���ܼĴ���1
    
    // ����Ƶ�ʲ���
    WriteData_AD9959(FREQS0_ADDR, 4, tempCFTWS, 0);  // ��ʼƵ��
    WriteData_AD9959(FREQE0_ADDR, 4, tempCFTWE, 0);  // ��ֹƵ��
    WriteData_AD9959(RDW_ADDR, 4, tempRDW, 0);      // ��������
    WriteData_AD9959(FDW_ADDR, 4, tempFDW, 0);      // �½�����
    WriteData_AD9959(LSRR_ADDR, 2, tempLSRR, 0);    // ͣ��ʱ��
    
    // ����IO��Ӧ������
    IO_Update();
    
    // ����ɨƵ - �Ƚ�����PSx�����øߣ�Ȼ����ѡͨ����PSx�����õ�
    GPIO_SetBits(AD9959_PS0_GPIO, AD9959_PS0_Pin);
    GPIO_SetBits(AD9959_PS1_GPIO, AD9959_PS1_Pin);
    GPIO_SetBits(AD9959_PS2_GPIO, AD9959_PS2_Pin);
    GPIO_SetBits(AD9959_PS3_GPIO, AD9959_PS3_Pin);
    delay1(10);

    // ����ѡͨ����PSx�����õ��Դ���ɨƵ
    switch (Channel) {
        case 0:
            GPIO_ResetBits(AD9959_PS0_GPIO, AD9959_PS0_Pin);
            break;
        case 1:
            GPIO_ResetBits(AD9959_PS1_GPIO, AD9959_PS1_Pin);
            break;
        case 2:
            GPIO_ResetBits(AD9959_PS2_GPIO, AD9959_PS2_Pin);
            break;
        case 3:
            GPIO_ResetBits(AD9959_PS3_GPIO, AD9959_PS3_Pin);
            break;
    }
    
    delay1(10);
}





void CalculateFreq(uint32_t Freq, uint8_t buf[4])
{
    uint32_t Temp;           
    Temp=(uint32_t)Freq*8.589934592;	   //������Ƶ�����ӷ�Ϊ�ĸ��ֽ�  8.589934592=(2^32)/500000000
    buf[3]=(uint8_t)Temp;
    buf[2]=(uint8_t)(Temp>>8);
    buf[1]=(uint8_t)(Temp>>16);
    buf[0]=(uint8_t)(Temp>>24);
}


void CalculatePhase(double phase, uint8_t buf[2])
{
    uint16_t P_temp=0;
    P_temp=(uint16_t)phase;
    buf[1]=(uint8_t)P_temp;
    buf[0]=(uint8_t)(P_temp>>8);
}

/////////////////////////////////////////////////////
//StayTime: unit: ns, max: 2040ns, min: 8ns, When SYSCLK=500MHz;
/////////////////////////////////////////////////////
void CalculateStayTime(double StayTime,uint8_t buf[2])
{
    double tempSysClk=SYSCLK/4;
    uint16_t LSRR=0;
    if(StayTime>2048)
        StayTime=2040;
    if(StayTime<8)
        StayTime=8;
    LSRR=StayTime/8;
    buf[0]=LSRR;
    buf[1]=LSRR;
}



/*---------------------------------------
�������ܣ�ѡ��AD9959���ض�ͨ��
Channel:  ���ͨ�� (0-3)
������
---------------------------------------*/
void Channel_Select(uint8_t Channel)
{
    // ȷ��ͨ�������Ϸ�
    if (Channel > 3) return;
    
    // ���Ƚ�����ͨ��ѡ�������õ�
    GPIO_ResetBits(AD9959_PS0_GPIO, AD9959_PS0_Pin);
    GPIO_ResetBits(AD9959_PS1_GPIO, AD9959_PS1_Pin);
    GPIO_ResetBits(AD9959_PS2_GPIO, AD9959_PS2_Pin);
    GPIO_ResetBits(AD9959_PS3_GPIO, AD9959_PS3_Pin);

    // ���ݴ����ͨ������������Ӧ�������ø�
    switch (Channel) {
        case 0:
            GPIO_SetBits(AD9959_PS0_GPIO, AD9959_PS0_Pin);
            break;
        case 1:
            GPIO_SetBits(AD9959_PS1_GPIO, AD9959_PS1_Pin);
            break;
        case 2:
            GPIO_SetBits(AD9959_PS2_GPIO, AD9959_PS2_Pin);
            break;
        case 3:
            GPIO_SetBits(AD9959_PS3_GPIO, AD9959_PS3_Pin);
            break;
    }
}

/*--------------------------------------------
�������ܣ�������ͨ��SPI��AD9959��ȡ����
RegisterAddress: �Ĵ�����ַ
NumberofRegisters: �����ֽ���
*RegisterData: ���ݴ洢��ʼ��ַ
----------------------------------------------*/
void ReadData_AD9959(uint8_t RegisterAddress, uint8_t NumberofRegisters, uint8_t *RegisterData)
{
    uint8_t ControlValue = 0;
    uint8_t i = 0;
    uint8_t RegisterIndex = 0;
    uint8_t receivedData = 0;

    ControlValue = RegisterAddress | 0x80;  // �������λΪ1����ʾ����һ��������
    
    // ����SDIO0Ϊ����ģʽ
    GPIO_InitTypeDef GPIO_InitStruct;
    GPIO_InitStruct.GPIO_Pin = AD9959_SDIO0_Pin;
    GPIO_InitStruct.GPIO_Mode = GPIO_Mode_IN;
    GPIO_InitStruct.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(AD9959_SDIO0_GPIO, &GPIO_InitStruct);
    
    // д���ַ
    AD9959_SCLK_0;
    AD9959_CS_0;
    
    // ���Ͷ������ַ+����־��
    for(i = 0; i < 8; i++)
    {
        AD9959_SCLK_0;
        if(0x80 == (ControlValue & 0x80))
        {
            AD9959_SDIO0_1;
        }
        else
        {
            AD9959_SDIO0_0;
        }
        AD9959_SCLK_1;
        ControlValue <<= 1;
    }
    
    AD9959_SCLK_0;
    
    // ��ȡ����
    for (RegisterIndex = 0; RegisterIndex < NumberofRegisters; RegisterIndex++)
    {
        receivedData = 0;
        for (i = 0; i < 8; i++)
        {
            AD9959_SCLK_0;
            AD9959_SCLK_1;
            
            // ��ȡSDIO0����״̬
            receivedData <<= 1;
            if (GPIO_ReadInputDataBit(AD9959_SDIO0_GPIO, AD9959_SDIO0_Pin) == Bit_SET)
            {
                receivedData |= 0x01;
            }
        }
        
        // ������յ�������
        RegisterData[RegisterIndex] = receivedData;
        AD9959_SCLK_0;
    }
    
    AD9959_CS_1;
    
    // �ָ�SDIO0Ϊ���ģʽ
    GPIO_InitStruct.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStruct.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStruct.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(AD9959_SDIO0_GPIO, &GPIO_InitStruct);
}
