/**
 ****************************************************************************************************
 * @file        dac8563.c
 * <AUTHOR>
 * @version     V1.0
 * @date        2020-04-21
 * @brief       spi驱动程序
 ****************************************************************************************************
 */
#ifndef __SPI_H
#define __SPI_H
#include "stm32f4xx.h"
#include <stdio.h>
#include <stdint.h>
#include "../SYSTEM/sys/sys.h"

extern SPI_TypeDef* SPI1_Handler; /* SPI1寄存器指针 */

/* SPI总线速度设置 */
#define SPI_SPEED_2         0
#define SPI_SPEED_4         1
#define SPI_SPEED_8         2
#define SPI_SPEED_16        3
#define SPI_SPEED_32        4
#define SPI_SPEED_64        5
#define SPI_SPEED_128       6

/*******************************外部函数声明*******************************************/  													  
void spi1_init(void);
void spi1_set_speed(uint8_t speed);
uint16_t spi1_read_write_16bit(uint16_t txdata);
		 
#endif
