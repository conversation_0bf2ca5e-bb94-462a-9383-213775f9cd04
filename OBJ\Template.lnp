--cpu=Cortex-M4.fp.sp
"..\obj\main.o"
"..\obj\stm32f4xx_it.o"
"..\obj\system_stm32f4xx.o"
"..\obj\startup_stm32f40_41xxx.o"
"..\obj\misc.o"
"..\obj\stm32f4xx_adc.o"
"..\obj\stm32f4xx_can.o"
"..\obj\stm32f4xx_crc.o"
"..\obj\stm32f4xx_cryp.o"
"..\obj\stm32f4xx_cryp_aes.o"
"..\obj\stm32f4xx_cryp_des.o"
"..\obj\stm32f4xx_cryp_tdes.o"
"..\obj\stm32f4xx_dac.o"
"..\obj\stm32f4xx_dbgmcu.o"
"..\obj\stm32f4xx_dcmi.o"
"..\obj\stm32f4xx_dma2d.o"
"..\obj\stm32f4xx_dma.o"
"..\obj\stm32f4xx_exti.o"
"..\obj\stm32f4xx_flash.o"
"..\obj\stm32f4xx_flash_ramfunc.o"
"..\obj\stm32f4xx_fsmc.o"
"..\obj\stm32f4xx_gpio.o"
"..\obj\stm32f4xx_hash.o"
"..\obj\stm32f4xx_hash_md5.o"
"..\obj\stm32f4xx_hash_sha1.o"
"..\obj\stm32f4xx_i2c.o"
"..\obj\stm32f4xx_iwdg.o"
"..\obj\stm32f4xx_ltdc.o"
"..\obj\stm32f4xx_pwr.o"
"..\obj\stm32f4xx_rcc.o"
"..\obj\stm32f4xx_rng.o"
"..\obj\stm32f4xx_rtc.o"
"..\obj\stm32f4xx_sai.o"
"..\obj\stm32f4xx_sdio.o"
"..\obj\stm32f4xx_spi.o"
"..\obj\stm32f4xx_syscfg.o"
"..\obj\stm32f4xx_tim.o"
"..\obj\stm32f4xx_usart.o"
"..\obj\stm32f4xx_wwdg.o"
"..\obj\delay.o"
"..\obj\sys.o"
"..\obj\usart.o"
"..\obj\stm32f4_key.o"
"..\obj\led.o"
"..\obj\g.o"
"..\obj\timer.o"
"..\obj\kalman.o"
"..\obj\adc.o"
"..\obj\fft.o"
"..\obj\lcd.o"
"..\obj\lcd_ex.o"
"..\obj\ad9959_new.o"
"..\obj\spi.o"
"..\DSP_LIB\arm_cortexM4lf_math.lib"
--strict --scatter "..\OBJ\Template.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "..\OBJ\Template.map" -o ..\OBJ\Template.axf